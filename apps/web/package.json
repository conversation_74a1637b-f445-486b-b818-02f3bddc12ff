{"name": "@vtchat/web", "version": "1.0.0", "description": "VTChat web application - AI-powered conversation platform", "private": true, "author": "<PERSON><PERSON>", "license": "MIT", "scripts": {"dev": "next dev --turbopack", "dev-no-turbo": "next dev", "dev:scan": "REACT_SCAN_ENABLED=true next dev --turbopack", "build": "next build", "build:turbo": "next build --turbopack", "analyze": "ANALYZE=true next build", "start": "next start", "lint": "ox<PERSON>", "prepare": "husky", "generate": "bunx drizzle-kit generate", "scan": "bunx react-scan localhost:3000", "test:gemini": "DATABASE_URL=postgresql://test:test@localhost:5432/test bun test app/tests/rate-limit-simple.test.ts app/tests/implementation-check.test.ts", "test:gemini-basic": "DATABASE_URL=postgresql://test:test@localhost:5432/test bun test app/tests/rate-limit-simple.test.ts", "test:gemini-implementation": "DATABASE_URL=postgresql://test:test@localhost:5432/test bun test app/tests/implementation-check.test.ts", "test:gemini-unit": "DATABASE_URL=postgresql://test:test@localhost:5432/test bun test app/tests/gemini-free-model.test.ts", "test:gemini-api": "DATABASE_URL=postgresql://test:test@localhost:5432/test bun test app/tests/gemini-api-integration.test.ts", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:performance": "playwright test lighthouse-performance.test.ts"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@anthropic-ai/sdk": "^0.56.0", "@google/generative-ai": "^0.24.1", "@neondatabase/serverless": "^1.0.1", "@radix-ui/react-slot": "^1.2.3", "@repo/ai": "*", "@repo/common": "*", "@repo/shared": "*", "@repo/ui": "*", "@tanstack/react-table": "^8.21.3", "ai": "5.0.0-beta.23", "better-auth": "^1.2.9", "better-auth-is-bot": "^1.0.0", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "crypto-browserify": "^3.12.1", "date-fns": "3.6.0", "drizzle-orm": "^0.44.2", "events": "^3.3.0", "geist": "^1.3.1", "immer": "^10.1.1", "ioredis": "^5.6.1", "isbot": "^5.1.28", "lucide-react": "^0.522.0", "nanoid": "^5.1.5", "next": "^15.4.1", "next-themes": "^0.4.6", "path-browserify": "^1.0.1", "querystring-es3": "^0.2.1", "react": "^19.1.0", "react-dom": "^19.1.0", "recharts": "^2.15.4", "sharp": "^0.34.2", "sonner": "^2.0.5", "stream-browserify": "^3.0.0", "tailwind-merge": "^3.3.1", "undici": "^7.11.0", "url": "^0.11.4", "use-stick-to-bottom": "^1.0.46", "util": "^0.12.5", "zod": "^3.25.49", "zustand": "^5.0.5"}, "devDependencies": {"@next/bundle-analyzer": "^15.4.1", "@playwright/test": "^1.53.2", "@repo/tailwind-config": "*", "@repo/typescript-config": "*", "@types/ioredis": "^5.0.0", "@types/node": "^20", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.31.4", "husky": "^9.1.4", "postcss": "^8.5.3", "postcss-load-config": "^6.0.1", "postgres": "^3.4.4", "tailwindcss": "3", "typescript": "^5"}}