"use client";

import { ShineText, UserTierBadge } from "@repo/common/components";
import { useCreemSubscription } from "@repo/common/hooks";
import { useGlobalSubscriptionStatus } from "@repo/common/providers/subscription-provider";
import { BUTTON_TEXT } from "@repo/shared/constants";
import { useSession } from "@repo/shared/lib/auth-client";
import {
    Announcement,
    AnnouncementTag,
    AnnouncementTitle,
    TypographyH2,
    TypographyH3,
    TypographyMuted,
} from "@repo/ui";
import { ArrowUpRight, Check, CheckCircle, Sparkles } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { AnimatedBadge } from "../../components/animated-badge";
import { ButtonAnimatedGradient } from "../../components/button-animated-gradient";
import { ButtonShadowGradient } from "../../components/button-shadow-gradient";
import { CardSpotlightPricing } from "../../components/card-spotlight-pricing";
import { FeaturesAccordion } from "../../components/features-accordion";
import { PRICING_CONFIG } from "../../lib/config/pricing";

export function PricingClient() {
    const { data: session, isPending: isSessionLoading } = useSession();
    const {
        isPlusSubscriber,
        isLoading: isSubscriptionLoading,
        refreshSubscriptionStatus,
    } = useGlobalSubscriptionStatus();
    const {
        startVtPlusSubscription,
        openCustomerPortal,
        isLoading: isPaymentLoading,
        isPortalLoading,
    } = useCreemSubscription();
    const router = useRouter();

    const isSignedIn = !!session?.user;
    const isLoaded = !isSessionLoading;
    const isLoading = isSessionLoading || isSubscriptionLoading;
    const isCurrentlySubscribed = isPlusSubscriber;
    const isFreeTier = isLoaded && !isPlusSubscriber;

    useEffect(() => {
        if (isLoaded && !isSignedIn) {
            router.push("/login?redirect_url=/pricing");
        }
    }, [isLoaded, isSignedIn, router]);

    useEffect(() => {
        if (isSignedIn && !isSubscriptionLoading) {
            refreshSubscriptionStatus();
        }
    }, [isSignedIn, refreshSubscriptionStatus, isSubscriptionLoading]);

    const handleSubscribe = async () => {
        if (!isSignedIn) {
            router.push("/login?redirect_url=/pricing");
            return;
        }

        if (isCurrentlySubscribed) {
            await openCustomerPortal();
        } else {
            await startVtPlusSubscription();
        }
    };

    const handleTryFree = () => {
        if (isSignedIn) {
            router.push("/");
        } else {
            router.push("/login?redirect_url=/");
        }
    };

    const getSubscribeButtonText = () => {
        if (isLoading || isPortalLoading) return BUTTON_TEXT.LOADING;
        if (!isSignedIn) return `Subscribe to ${PRICING_CONFIG.product.name}`;
        if (isCurrentlySubscribed) {
            return (
                <>
                    {isPortalLoading ? BUTTON_TEXT.LOADING : BUTTON_TEXT.MANAGE_SUBSCRIPTION}
                    <UserTierBadge className="ml-2" />
                </>
            );
        }
        return `Upgrade to ${PRICING_CONFIG.product.name}`;
    };

    const getFreeButtonText = () => {
        if (isLoading) return BUTTON_TEXT.LOADING;
        if (!isSignedIn) return "Sign Up Free";
        if (isCurrentlySubscribed) return "Continue to Chat";
        return "Continue";
    };

    const getCTAButtonText = () => {
        if (isLoading || isPortalLoading) return BUTTON_TEXT.LOADING;
        if (!isSignedIn) return `Start Your ${PRICING_CONFIG.product.name} Journey`;
        if (isCurrentlySubscribed) {
            return (
                <>
                    {isPortalLoading ? BUTTON_TEXT.LOADING : BUTTON_TEXT.MANAGE_SUBSCRIPTION}
                    {!isPortalLoading && <UserTierBadge className="ml-2" />}
                </>
            );
        }
        return `Upgrade to ${PRICING_CONFIG.product.name}`;
    };

    return (
        <div className="relative min-h-dvh w-full">
            <div className="container relative z-10 mx-auto px-2 py-4 pt-8 md:px-4 md:py-8 md:pt-16">
                {/* Hero Section */}
                <div className="mb-4 space-y-3 pt-4 text-center md:mb-8 md:pt-8">
                    <div className="space-y-2 md:space-y-3">
                        <AnimatedBadge>
                            <Sparkles className="mr-2 h-4 w-4" />
                            {PRICING_CONFIG.product.name}
                        </AnimatedBadge>

                        <div>
                            <ShineText className="text-3xl font-bold leading-tight tracking-tight md:text-5xl md:leading-loose lg:text-6xl">
                                Advanced AI features
                            </ShineText>
                            <p className="mt-4 text-lg text-gray-600 md:text-xl">
                                Most powerful AI capabilities included free. VT+ adds 3 exclusive
                                research features.
                            </p>
                        </div>
                    </div>
                </div>

                {/* VT LAUNCH Promo Announcement */}
                <div className="mb-6 flex justify-center">
                    <Announcement themed>
                        <AnnouncementTag>Welcome</AnnouncementTag>
                        <AnnouncementTitle>
                            Get 20% off VT+ with code VTLAUNCH
                            <ArrowUpRight className="shrink-0 text-muted-foreground" size={16} />
                        </AnnouncementTitle>
                    </Announcement>
                </div>

                {/* Pricing Section */}
                <div className="relative px-2 py-4 md:px-6 md:py-6 lg:px-8" id="pricing">
                    <div className="mx-auto grid max-w-lg grid-cols-1 items-center gap-0 lg:max-w-4xl lg:grid-cols-2">
                        {/* Free Plan Card */}
                        <CardSpotlightPricing
                            className={`rounded-3xl rounded-t-3xl bg-white p-4 ring-1 sm:mx-8 sm:rounded-b-none sm:p-8 md:p-10 lg:mx-0 lg:rounded-bl-3xl lg:rounded-tr-none ${
                                isFreeTier ? "ring-2 ring-[#BFB38F]" : "ring-gray-900/10"
                            }`}
                        >
                            <div>
                                <div className="flex items-center justify-between">
                                    <TypographyH3
                                        className="text-lg font-bold text-[#BFB38F]"
                                        id="tier-free"
                                    >
                                        Free
                                    </TypographyH3>
                                    {isFreeTier && (
                                        <div className="flex items-center gap-2 rounded-full bg-[#BFB38F]/10 px-3 py-1">
                                            <CheckCircle className="h-4 w-4 text-[#BFB38F]" />
                                            <span className="text-sm font-medium text-[#BFB38F]">
                                                Current Plan
                                            </span>
                                        </div>
                                    )}
                                </div>
                                <p className="mt-4 flex items-baseline gap-x-2">
                                    <span className="text-5xl font-semibold tracking-tight text-gray-900">
                                        ${PRICING_CONFIG.pricing.free.price}
                                    </span>
                                    <span className="text-base text-gray-500">
                                        /{PRICING_CONFIG.pricing.free.interval}
                                    </span>
                                </p>
                                <p className="mt-6 text-base/7 text-gray-600">
                                    VT offers free tier, and with VT+ focusing only on 3 exclusive
                                    research capabilities: Deep Research, Pro Search, and RAG
                                    (Personal AI Assistant with Memory).
                                </p>
                                <ul className="mt-8 space-y-3 text-sm/6 text-gray-600 sm:mt-10">
                                    {PRICING_CONFIG.pricing.free.features.map((feature, index) => (
                                        <li className="flex gap-x-3" key={index}>
                                            <Check className="h-6 w-5 flex-none text-[#BFB38F]" />
                                            {typeof feature === "string" ? feature : feature.name}
                                        </li>
                                    ))}
                                </ul>
                                <div className="mt-8 sm:mt-10">
                                    <ButtonShadowGradient
                                        className="w-full"
                                        data-vmtrc="FreePlanSelected"
                                        data-vmtrc-context="pricing_page"
                                        data-vmtrc-plan="VT_BASE"
                                        onClick={handleTryFree}
                                    >
                                        {getFreeButtonText()}
                                    </ButtonShadowGradient>
                                </div>
                            </div>
                        </CardSpotlightPricing>

                        {/* VT+ Plan Card */}
                        <div className="relative inline-block overflow-hidden rounded-3xl p-[1px]">
                            <span className="absolute inset-[-1000%] animate-[spin_2s_linear_infinite] bg-[conic-gradient(from_90deg_at_50%_50%,#BFB38F_0%,#262626_50%,#BFB38F_100%)]" />
                            <CardSpotlightPricing
                                className={`relative rounded-3xl bg-gray-900 p-8 shadow-2xl ring-1 sm:p-10 ${
                                    isCurrentlySubscribed
                                        ? "ring-2 ring-[#BFB38F]"
                                        : "ring-gray-900/10"
                                }`}
                            >
                                <div>
                                    <div className="flex items-center justify-between">
                                        <TypographyH3
                                            className="text-lg font-bold text-[#BFB38F]"
                                            id="tier-vt-plus"
                                        >
                                            {PRICING_CONFIG.product.name}
                                        </TypographyH3>
                                        {isCurrentlySubscribed && (
                                            <div className="flex items-center gap-2 rounded-full bg-[#BFB38F]/20 px-3 py-1">
                                                <CheckCircle className="h-4 w-4 text-[#BFB38F]" />
                                                <span className="text-sm font-medium text-[#BFB38F]">
                                                    Current Plan
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                    <p className="mt-4 flex items-baseline gap-x-2">
                                        <span className="text-5xl font-semibold tracking-tight text-white">
                                            ${PRICING_CONFIG.pricing.plus.price}
                                        </span>
                                        <span className="text-base text-gray-400">
                                            /{PRICING_CONFIG.pricing.plus.interval}
                                        </span>
                                    </p>
                                    <p className="mt-6 text-base/7 text-gray-300">
                                        {PRICING_CONFIG.product.description}
                                    </p>

                                    <ul className="mt-8 space-y-3 text-sm/6 text-gray-300 sm:mt-10">
                                        {PRICING_CONFIG.pricing.plus.features.map(
                                            (feature, index) => (
                                                <li className="flex gap-x-3" key={index}>
                                                    <Check className="h-6 w-5 flex-none text-[#BFB38F]" />
                                                    <div className="flex items-center gap-2">
                                                        <span>
                                                            {typeof feature === "string"
                                                                ? feature
                                                                : feature.name}
                                                        </span>
                                                    </div>
                                                </li>
                                            ),
                                        )}
                                    </ul>
                                    <div className="mt-8 sm:mt-10">
                                        <ButtonAnimatedGradient
                                            className="flex w-full items-center justify-center"
                                            data-vmtrc="PremiumPlanSelected"
                                            data-vmtrc-context="pricing_page"
                                            data-vmtrc-plan="VT_PLUS"
                                            data-vmtrc-price="10"
                                            onClick={() => {
                                                if (!(isPortalLoading || isPaymentLoading)) {
                                                    handleSubscribe();
                                                }
                                            }}
                                        >
                                            {getSubscribeButtonText()}
                                        </ButtonAnimatedGradient>
                                    </div>

                                    {/* Free Trial & Cancel Anytime Badge */}
                                    <div className="mt-4 flex items-center justify-center gap-4 text-sm">
                                        <TypographyMuted>Free trial included</TypographyMuted>
                                        <TypographyMuted>Cancel anytime</TypographyMuted>
                                    </div>
                                    {/* Terms and Privacy Links */}
                                    <div className="mt-4 text-center text-sm text-gray-400">
                                        <span className="text-gray-500">Please review our</span>{" "}
                                        <a
                                            className="underline transition-colors hover:text-[#BFB38F]"
                                            href="/terms"
                                        >
                                            Terms of Service
                                        </a>{" "}
                                        <span>and</span>{" "}
                                        <a
                                            className="underline transition-colors hover:text-[#BFB38F]"
                                            href="/privacy"
                                        >
                                            Privacy Policy
                                        </a>{" "}
                                        <span className="text-gray-500">before subscribing</span>
                                    </div>
                                </div>
                            </CardSpotlightPricing>
                        </div>
                    </div>
                </div>

                {/* Features Section */}
                <div className="mb-8 mt-8">
                    <div className="mb-6 text-center">
                        <ShineText className="text-4xl font-bold leading-relaxed tracking-tight sm:text-4xl">
                            Powerful Features
                        </ShineText>
                        <p className="leading-7 [&:not(:first-child)]:mt-6">
                            Discover what makes {PRICING_CONFIG.product.name} the perfect choice for
                            your productivity needs
                        </p>
                    </div>
                    <FeaturesAccordion />
                </div>

                {/* CTA Section */}
                <div className="mt-8 space-y-4 text-center">
                    <TypographyH2 className="text text-lg font-semibold">
                        Ready to get started?
                    </TypographyH2>
                    <div className="mx-auto max-w-md pt-4">
                        <ButtonAnimatedGradient
                            className="flex w-full items-center justify-center"
                            onClick={() => {
                                if (!(isPortalLoading || isPaymentLoading)) {
                                    handleSubscribe();
                                }
                            }}
                        >
                            {getCTAButtonText()}
                        </ButtonAnimatedGradient>
                    </div>
                </div>

                {/* Contact Section */}
                <div className="mb-8 mt-8 text-center">
                    <p className="text-base text-gray-600">
                        Have questions? Get in touch:{" "}
                        <a
                            className="font-medium text-[#BFB38F] transition-colors hover:text-[#BFB38F]/80"
                            href="mailto:<EMAIL>"
                        >
                            <EMAIL>
                        </a>
                    </p>
                </div>
            </div>
        </div>
    );
}
