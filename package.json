{"name": "vtchat", "version": "1.0.0", "description": "An intelligent chat application powered by AI with advanced conversational capabilities", "private": true, "author": "<PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vinhnx/vtchat.git"}, "keywords": ["ai", "chat", "conversation", "nextjs", "react", "typescript", "tailwindcss"], "homepage": "https://github.com/vinhnx/vtchat#readme", "bugs": {"url": "https://github.com/vinhnx/vtchat/issues"}, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "dev-no-turbo": "turbo run dev-no-turbo", "dev:scan": "REACT_SCAN_ENABLED=true turbo run dev", "start": "cd apps/web && bun start", "lint": "ox<PERSON>", "lint:urls": "node scripts/check-hardcoded-urls.mjs", "clean": "turbo run clean && rm -rf node_modules", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md}\"", "biome:check": "biome check --write .", "biome:lint": "biome lint --write .", "biome:format": "biome format --write .", "biome:format-files": "biome format --write", "prepare": "husky", "knip": "knip", "bundle:analyze": "cd apps/web && ANALYZE=true bun run build", "bundle:track": "node scripts/track-bundle-size.js track", "bundle:history": "node scripts/track-bundle-size.js history", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:watch": "vitest --watch", "test:coverage": "vitest run --coverage"}, "devDependencies": {"@better-auth/cli": "^1.2.9", "@biomejs/biome": "2.0.6", "@flydotio/dockerfile": "^0.7.10", "@tailwindcss/typography": "^0.5.0-alpha.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.0.3", "@vitejs/plugin-react": "^4.5.2", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "10.4.21", "drizzle-kit": "^0.31.4", "globals": "^16.0.0", "happy-dom": "^18.0.1", "jsdom": "^26.1.0", "knip": "^5.61.1", "oxlint": "^1.1.0", "postcss": "8.5.4", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.12", "tailwindcss": "3.4.17", "tsconfig-paths-webpack-plugin": "^4.2.0", "turbo": "latest", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "packageManager": "bun@1.1.19", "engines": {"node": ">=20.0.0", "bun": ">=1.1.19"}, "workspaces": ["apps/*", "packages/*", "!apps/*/.next", "!**/node_modules"], "dependencies": {"@ai-sdk/google": "2.0.0-beta.12", "@better-auth/utils": "^0.2.6", "@mozilla/readability": "^0.6.0", "@neondatabase/serverless": "^1.0.1", "@tanstack/react-table": "^8.21.3", "@types/pino": "^7.0.5", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/react-window": "^1.8.8", "@vercel/functions": "^2.2.0", "better-auth": "^1.2.9", "better-auth-harmony": "^1.2.5", "creem": "^0.3.37", "date-fns": "3.6.0", "drizzle-orm": "^0.44.2", "husky": "^9.1.7", "isbot": "^5.1.28", "legid": "^0.1.3", "next": "15.4.1", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "node-html-parser": "^7.0.1", "pdfjs-dist": "^5.3.31", "pg": "^8.16.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-intersection-observer": "^9.16.0", "react-scan": "^0.4.3", "react-window": "^1.8.11", "remark-slug": "^8.0.0", "turndown": "^7.2.0", "zustand": "^5.0.5"}, "overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}