# Design Document

## Overview

This design document outlines the implementation of Gemini image generation capabilities in VT using Google's Imagen models through the AI SDK v5. The feature will integrate seamlessly with the existing AI provider system and chat interface, allowing users to generate images using text prompts while maintaining VT's privacy-first architecture.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[User Interface] --> B[Model Selection]
    B --> C[Chat Input Handler]
    C --> D[AI Provider System]
    D --> E[Google Generative AI Provider]
    E --> F[Imagen API]
    F --> G[Generated Image]
    G --> H[Local Storage - IndexedDB]
    G --> I[Chat Display]
```

### Integration Points

1. **Model Configuration**: Extend existing model definitions to include image generation models
2. **Provider System**: Leverage existing Google provider with image generation capabilities
3. **Chat Interface**: Adapt current chat UI to handle image generation mode
4. **Storage System**: Use existing IndexedDB storage for generated images
5. **Error Handling**: Integrate with existing error handling system

## Components and Interfaces

### 1. Model Configuration Extensions

**File**: `packages/ai/models.ts`

Add new image generation models to the ModelEnum and models array:

```typescript
export const ModelEnum = {
    // ... existing models
    GEMINI_IMAGEN_3_0: 'imagen-3.0-generate-001',
    GEMINI_IMAGEN_3_0_FAST: 'imagen-3.0-fast-generate-001',
} as const;

export type ImageModel = {
    id: ModelEnum;
    name: string;
    provider: ProviderEnumType;
    type: 'image'; // New field to distinguish image models
    maxPromptLength: number;
    aspectRatios: string[]; // Supported aspect ratios
};
```

### 2. Chat Mode Configuration

**File**: `packages/shared/config/chat-mode.ts`

Add new ChatMode entries for image generation:

```typescript
export const ChatMode = {
    // ... existing modes
    GEMINI_IMAGEN_3_0: 'gemini-imagen-3.0',
    GEMINI_IMAGEN_3_0_FAST: 'gemini-imagen-3.0-fast',
} as const;
```

### 3. Provider System Enhancement

**File**: `packages/ai/providers.ts`

Extend the `getLanguageModel` function to handle image generation models:

```typescript
export const getImageModel = (
    m: ModelEnum,
    byokKeys?: Record<string, string>,
    isVtPlus?: boolean
) => {
    // Similar to getLanguageModel but for image generation
    // Uses generateImage from AI SDK instead of generateText
};
```

### 4. Chat Interface Adaptations

**File**: `packages/common/components/chat-input/`

Create new components:

- `ImageGenerationInput.tsx`: Specialized input for image prompts
- `ImageModelSelector.tsx`: Model selector showing only image models
- `ImagePreview.tsx`: Component to display generated images

### 5. Message Type Extensions

**File**: `packages/shared/types/`

Extend message types to support image content:

```typescript
export interface ImageMessage extends BaseMessage {
    type: 'image';
    content: {
        prompt: string;
        imageUrl: string;
        imageData: Uint8Array;
        model: string;
        aspectRatio?: string;
    };
}
```

### 6. Storage System Integration

**File**: `packages/common/lib/storage/`

Extend IndexedDB schema to store generated images:

```typescript
interface ImageRecord {
    id: string;
    threadId: string;
    messageId: string;
    imageData: Uint8Array;
    prompt: string;
    model: string;
    createdAt: Date;
}
```

## Data Models

### Image Generation Request

```typescript
interface ImageGenerationRequest {
    prompt: string;
    model: ModelEnum;
    aspectRatio?: '1:1' | '16:9' | '9:16' | '4:3' | '3:4';
    apiKey: string;
}
```

### Image Generation Response

```typescript
interface ImageGenerationResponse {
    success: boolean;
    imageData?: Uint8Array;
    imageUrl?: string;
    error?: string;
    metadata?: {
        model: string;
        prompt: string;
        aspectRatio: string;
        generatedAt: Date;
    };
}
```

### UI State Management

```typescript
interface ImageGenerationState {
    isImageMode: boolean;
    selectedImageModel: ModelEnum | null;
    isGenerating: boolean;
    generationProgress?: number;
    lastGeneratedImage?: ImageGenerationResponse;
}
```

## Error Handling

### Error Categories

1. **API Key Errors**
    - Missing Google API key
    - Invalid API key
    - Quota exceeded

2. **Content Policy Violations**
    - Inappropriate prompts
    - Safety filter triggers

3. **Technical Errors**
    - Network failures
    - Rate limiting
    - Service unavailable

4. **Client-side Errors**
    - Storage failures
    - Image processing errors

### Error Response Format

```typescript
interface ImageGenerationError {
    code: 'API_KEY_MISSING' | 'CONTENT_POLICY' | 'RATE_LIMIT' | 'NETWORK_ERROR' | 'QUOTA_EXCEEDED';
    message: string;
    details?: string;
    retryable: boolean;
    suggestedAction?: string;
}
```

## Testing Strategy

### Unit Tests

1. **Model Configuration Tests**
    - Verify image models are properly defined
    - Test model selection logic
    - Validate ChatMode mappings

2. **Provider Integration Tests**
    - Test image generation API calls
    - Verify error handling
    - Test API key validation

3. **UI Component Tests**
    - Image input component functionality
    - Model selector behavior
    - Image display components

### Integration Tests

1. **End-to-End Image Generation**
    - Complete flow from prompt to display
    - Error scenarios
    - Storage integration

2. **Chat Interface Integration**
    - Mode switching between text and image
    - Conversation history with images
    - Export functionality

### Manual Testing Scenarios

1. **Happy Path Testing**
    - Generate images with valid prompts
    - Switch between image models
    - View generated images in chat history

2. **Error Handling Testing**
    - Test with missing API key
    - Test with inappropriate prompts
    - Test network failure scenarios

3. **Privacy Testing**
    - Verify images are stored locally only
    - Test conversation export with images
    - Verify image deletion on history clear

## Implementation Phases

### Phase 1: Core Infrastructure

- Add image models to configuration
- Extend provider system for image generation
- Basic error handling

### Phase 2: UI Integration

- Create image generation input components
- Implement model selector for image models
- Basic image display functionality

### Phase 3: Storage and Privacy

- Implement IndexedDB storage for images
- Add privacy controls
- Conversation export with images

### Phase 4: Polish and Optimization

- Advanced error handling and user feedback
- Performance optimizations
- Comprehensive testing

## Security Considerations

1. **API Key Security**
    - API keys handled through existing BYOK system
    - No server-side storage of user API keys

2. **Content Safety**
    - Rely on Google's built-in safety filters
    - Display policy violation messages to users

3. **Privacy Protection**
    - All generated images stored locally in IndexedDB
    - No image data sent to VT servers
    - User control over image retention

4. **Resource Management**
    - Implement reasonable limits on image storage
    - Provide cleanup mechanisms for old images

## Performance Considerations

1. **Image Storage Optimization**
    - Compress images for storage efficiency
    - Implement lazy loading for image history
    - Provide storage usage indicators

2. **Network Optimization**
    - Efficient handling of large image responses
    - Progress indicators for generation
    - Retry mechanisms for failed requests

3. **UI Responsiveness**
    - Non-blocking image generation
    - Smooth transitions between modes
    - Optimized image rendering

## Accessibility

1. **Screen Reader Support**
    - Alt text for generated images
    - Descriptive labels for image controls
    - Keyboard navigation support

2. **Visual Accessibility**
    - High contrast mode support
    - Scalable image previews
    - Clear visual indicators for image mode

3. **Motor Accessibility**
    - Large touch targets
    - Keyboard shortcuts
    - Voice input support where applicable
