import { log } from "@repo/shared/logger";
import { generateImage } from "ai";
import type { ModelEnum } from "../models";
import { getImageModel as getImageModelInfo } from "../models";
import { getImageModel } from "../providers";
import {
    generateErrorMessage,
    getInvalidApiKeyError,
    getMissingApiKeyError,
    getNetworkError,
    getRateLimitError,
} from "./error-messages";

/**
 * Interface for image generation parameters
 */
export interface ImageGenerationParams {
    prompt: string;
    model: ModelEnum;
    aspectRatio?: string;
    apiKey?: string;
    byokKeys?: Record<string, string>;
    isVtPlus?: boolean;
}

/**
 * Interface for image generation response
 */
export interface ImageGenerationResponse {
    success: boolean;
    imageData?: Uint8Array;
    imageUrl?: string;
    error?: string;
    errorType?: ImageGenerationErrorType;
    metadata?: {
        model: string;
        prompt: string;
        aspectRatio: string;
        generatedAt: Date;
        dimensions?: string;
        size?: number;
    };
    retryable?: boolean;
}

/**
 * Enum for image generation error types
 */
export enum ImageGenerationErrorType {
    API_KEY_MISSING = "API_KEY_MISSING",
    API_KEY_INVALID = "API_KEY_INVALID",
    CONTENT_POLICY = "CONTENT_POLICY",
    RATE_LIMIT = "RATE_LIMIT",
    QUOTA_EXCEEDED = "QUOTA_EXCEEDED",
    NETWORK_ERROR = "NETWORK_ERROR",
    INVALID_PROMPT = "INVALID_PROMPT",
    UNKNOWN_ERROR = "UNKNOWN_ERROR",
}

/**
 * Validates the prompt for image generation
 * @param prompt The prompt to validate
 * @param model The model to use for generation
 * @returns An error message if validation fails, null otherwise
 */
export const validateImagePrompt = (prompt: string, model: ModelEnum): string | null => {
    // Get the image model info to check max prompt length
    const imageModelInfo = getImageModelInfo(model);

    if (!imageModelInfo) {
        return "Invalid image model selected";
    }

    if (!prompt || prompt.trim().length === 0) {
        return "Prompt cannot be empty";
    }

    if (prompt.length > imageModelInfo.maxPromptLength) {
        return `Prompt exceeds maximum length of ${imageModelInfo.maxPromptLength} characters`;
    }

    // Check for potentially problematic content (basic check)
    const problematicTerms = [
        "nude",
        "naked",
        "pornography",
        "porn",
        "explicit",
        "sexual",
        "violence",
        "gore",
        "blood",
        "terrorist",
        "child",
        "minor",
        "illegal",
    ];

    const lowerPrompt = prompt.toLowerCase();
    for (const term of problematicTerms) {
        if (lowerPrompt.includes(term)) {
            return `Your prompt may violate content policies. Please revise it to avoid terms related to ${term}.`;
        }
    }

    return null;
};

/**
 * Determines the error type from an error message
 * @param error The error message or object
 * @returns The appropriate error type
 */
export const determineErrorType = (error: Error | string): ImageGenerationErrorType => {
    const errorMessage = typeof error === "string" ? error : error.message;
    const errorLower = errorMessage.toLowerCase();

    if (errorLower.includes("api key required") || errorLower.includes("missing api key")) {
        return ImageGenerationErrorType.API_KEY_MISSING;
    }

    if (
        errorLower.includes("invalid api key") ||
        errorLower.includes("unauthorized") ||
        errorLower.includes("forbidden") ||
        errorLower.includes("401") ||
        errorLower.includes("403")
    ) {
        return ImageGenerationErrorType.API_KEY_INVALID;
    }

    if (
        errorLower.includes("rate limit") ||
        errorLower.includes("too many requests") ||
        errorLower.includes("429")
    ) {
        return ImageGenerationErrorType.RATE_LIMIT;
    }

    if (
        errorLower.includes("quota exceeded") ||
        errorLower.includes("usage limit") ||
        errorLower.includes("billing")
    ) {
        return ImageGenerationErrorType.QUOTA_EXCEEDED;
    }

    if (
        errorLower.includes("network") ||
        errorLower.includes("connection") ||
        errorLower.includes("timeout") ||
        errorLower.includes("econnrefused")
    ) {
        return ImageGenerationErrorType.NETWORK_ERROR;
    }

    if (
        errorLower.includes("content") ||
        errorLower.includes("policy") ||
        errorLower.includes("safety") ||
        errorLower.includes("harmful") ||
        errorLower.includes("inappropriate")
    ) {
        return ImageGenerationErrorType.CONTENT_POLICY;
    }

    if (errorLower.includes("prompt") || errorLower.includes("invalid input")) {
        return ImageGenerationErrorType.INVALID_PROMPT;
    }

    return ImageGenerationErrorType.UNKNOWN_ERROR;
};

/**
 * Generates an image using the specified model and prompt
 * @param params Image generation parameters
 * @returns Promise resolving to the image generation response
 */
export const generateImageFromPrompt = async (
    params: ImageGenerationParams,
): Promise<ImageGenerationResponse> => {
    const { prompt, model, aspectRatio, byokKeys, isVtPlus } = params;

    log.info("Generating image with parameters:", {
        model,
        promptLength: prompt.length,
        aspectRatio,
        hasByokKeys: !!byokKeys,
        isVtPlus,
    });

    // Validate the prompt before sending to the API
    const validationError = validateImagePrompt(prompt, model);
    if (validationError) {
        log.warn("Image prompt validation failed:", { error: validationError });
        return {
            success: false,
            error: validationError,
            errorType: ImageGenerationErrorType.INVALID_PROMPT,
            retryable: false,
        };
    }

    // Check for API key before making the request
    if (!byokKeys?.GEMINI_API_KEY && !isVtPlus) {
        const errorMsg = getMissingApiKeyError({
            provider: "google",
            hasApiKey: false,
            isVtPlus,
        });

        return {
            success: false,
            error: errorMsg.message,
            errorType: ImageGenerationErrorType.API_KEY_MISSING,
            retryable: false,
        };
    }

    try {
        // Get the image model
        const imageModel = getImageModel(model, byokKeys, isVtPlus);

        // Generate the image
        const result = await generateImage({
            model: imageModel,
            prompt,
            aspectRatio,
        });

        log.info("Image generation successful", {
            model,
            mimeType: result.image.mimeType,
            size: result.image.uint8Array.length,
        });

        // Calculate image dimensions if available
        let dimensions: string | undefined;
        if (result.image.width && result.image.height) {
            dimensions = `${result.image.width}x${result.image.height}`;
        }

        // Return the successful response with enhanced metadata
        return {
            success: true,
            imageData: result.image.uint8Array,
            metadata: {
                model: model,
                prompt,
                aspectRatio: aspectRatio || "1:1",
                generatedAt: new Date(),
                dimensions,
                size: result.image.uint8Array.length,
            },
        };
    } catch (error) {
        // Handle errors with specific error types
        log.error("Image generation failed:", {
            error: error instanceof Error ? error.message : String(error),
            model,
            promptLength: prompt.length,
        });

        const errorMessage = error instanceof Error ? error.message : String(error);
        const errorType = determineErrorType(error);

        // Generate appropriate user-friendly error message based on error type
        let friendlyError;
        let retryable = false;

        switch (errorType) {
            case ImageGenerationErrorType.API_KEY_MISSING:
                friendlyError = getMissingApiKeyError({
                    provider: "google",
                    hasApiKey: !!byokKeys?.GEMINI_API_KEY,
                    isVtPlus,
                });
                retryable = false;
                break;

            case ImageGenerationErrorType.API_KEY_INVALID:
                friendlyError = getInvalidApiKeyError({
                    provider: "google",
                    originalError: errorMessage,
                    hasApiKey: !!byokKeys?.GEMINI_API_KEY,
                    isVtPlus,
                });
                retryable = false;
                break;

            case ImageGenerationErrorType.RATE_LIMIT:
                friendlyError = getRateLimitError({
                    provider: "google",
                    hasApiKey: !!byokKeys?.GEMINI_API_KEY,
                    isVtPlus,
                });
                retryable = true;
                break;

            case ImageGenerationErrorType.QUOTA_EXCEEDED:
                friendlyError = getQuotaExceededError({
                    provider: "google",
                    hasApiKey: !!byokKeys?.GEMINI_API_KEY,
                    isVtPlus,
                });
                retryable = false;
                break;

            case ImageGenerationErrorType.NETWORK_ERROR:
                friendlyError = getNetworkError({
                    provider: "google",
                    originalError: errorMessage,
                });
                retryable = true;
                break;

            case ImageGenerationErrorType.CONTENT_POLICY:
                friendlyError = {
                    title: "Content Policy Violation",
                    message:
                        "Your prompt may violate Google's content policies. Please revise your prompt to avoid potentially harmful or inappropriate content.",
                    action: "Try a different prompt that complies with content policies",
                };
                retryable = false;
                break;

            default:
                friendlyError = generateErrorMessage("Image generation failed", {
                    provider: "google",
                    error: errorMessage,
                    hasApiKey: !!byokKeys?.GEMINI_API_KEY,
                    isVtPlus,
                });
                retryable = true;
        }

        return {
            success: false,
            error: friendlyError.message,
            errorType,
            retryable,
        };
    }
};

/**
 * Extracts metadata from an image
 * @param imageData The image data as a Uint8Array
 * @returns Metadata object with image information
 */
export const extractImageMetadata = (
    imageData: Uint8Array,
): {
    size: number;
    format: string;
    dimensions?: { width: number; height: number };
} => {
    // Basic format detection based on magic numbers
    let format = "unknown";
    if (imageData.length > 2) {
        // JPEG signature
        if (imageData[0] === 0xff && imageData[1] === 0xd8) {
            format = "jpeg";
        }
        // PNG signature
        else if (
            imageData[0] === 0x89 &&
            imageData[1] === 0x50 &&
            imageData[2] === 0x4e &&
            imageData[3] === 0x47
        ) {
            format = "png";
        }
        // WebP signature
        else if (
            imageData[0] === 0x52 &&
            imageData[1] === 0x49 &&
            imageData[2] === 0x46 &&
            imageData[3] === 0x46
        ) {
            format = "webp";
        }
    }

    return {
        size: imageData.length,
        format,
        // Note: Actual dimensions extraction would require image parsing libraries
        // which might be too heavy for this implementation
    };
};

/**
 * Checks if the user has reached their rate limit for image generation
 * @param userId The user ID
 * @param isVtPlus Whether the user has VT+ subscription
 * @returns Whether the user has reached their rate limit
 */
export const checkImageGenerationRateLimit = async (
    userId: string,
    isVtPlus: boolean,
): Promise<{ limited: boolean; reason?: string; resetTime?: Date }> => {
    // This is a placeholder implementation
    // In a real implementation, this would check a rate limit database or cache

    // For now, we'll just return no rate limit
    return { limited: false };

    // Example implementation:
    /*
    try {
        // Get current usage from database or cache
        const currentUsage = await getImageGenerationUsage(userId);

        // Define limits based on subscription
        const dailyLimit = isVtPlus ? 50 : 10;

        if (currentUsage.daily >= dailyLimit) {
            return {
                limited: true,
                reason: `Daily limit of ${dailyLimit} images reached`,
                resetTime: currentUsage.resetTime
            };
        }

        return { limited: false };
    } catch (error) {
        log.error("Error checking image generation rate limit:", {
            error: error instanceof Error ? error.message : String(error),
            userId,
            isVtPlus
        });

        // Default to not limited on error to avoid blocking users
        return { limited: false };
    }
    */
};

/**
 * Compresses image data for efficient storage
 * @param imageData The image data to compress
 * @returns Compressed image data
 */
export const compressImageData = (imageData: Uint8Array): Uint8Array => {
    // This is a placeholder implementation
    // In a real implementation, this would use a compression library
    // For now, we'll just return the original data

    return imageData;

    // Example implementation with compression:
    /*
    try {
        // Use a compression library to reduce image size
        // This would depend on the available libraries in the project
        const compressed = someCompressionLibrary.compress(imageData);

        log.info("Image compressed successfully", {
            originalSize: imageData.length,
            compressedSize: compressed.length,
            compressionRatio: imageData.length / compressed.length
        });

        return compressed;
    } catch (error) {
        log.error("Error compressing image data:", {
            error: error instanceof Error ? error.message : String(error),
            imageSize: imageData.length
        });

        // Return original data if compression fails
        return imageData;
    }
    */
};

/**
 * Generates a unique ID for an image
 * @returns A unique ID string
 */
export const generateImageId = (): string => {
    return `img_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
};

/**
 * Prepares an image for storage in IndexedDB
 * @param imageData The image data
 * @param metadata The image metadata
 * @returns Object ready for storage
 */
export const prepareImageForStorage = (
    imageData: Uint8Array,
    metadata: ImageGenerationResponse["metadata"],
): {
    id: string;
    imageData: Uint8Array;
    metadata: ImageGenerationResponse["metadata"];
    createdAt: Date;
} => {
    return {
        id: generateImageId(),
        imageData: compressImageData(imageData),
        metadata,
        createdAt: new Date(),
    };
};
/**
 * Error handling for content policy violations
 * @param errorMessage The original error message
 * @returns A user-friendly error response
 */
export const handleContentPolicyViolation = (errorMessage: string): ImageGenerationResponse => {
    log.warn("Content policy violation detected:", { error: errorMessage });

    // Extract specific policy information if available
    let specificPolicy = "content policies";

    if (errorMessage.toLowerCase().includes("violence")) {
        specificPolicy = "violence policies";
    } else if (errorMessage.toLowerCase().includes("sexual")) {
        specificPolicy = "sexual content policies";
    } else if (errorMessage.toLowerCase().includes("harassment")) {
        specificPolicy = "harassment policies";
    } else if (errorMessage.toLowerCase().includes("hate")) {
        specificPolicy = "hate speech policies";
    }

    return {
        success: false,
        error: `Your prompt violates Google's ${specificPolicy}. Please revise your prompt to avoid potentially harmful or inappropriate content.`,
        errorType: ImageGenerationErrorType.CONTENT_POLICY,
        retryable: false,
    };
};

/**
 * Validates API key for image generation
 * @param apiKey The API key to validate
 * @returns Whether the API key is valid for image generation
 */
export const validateImageGenerationApiKey = async (
    apiKey: string,
): Promise<{ valid: boolean; error?: string }> => {
    if (!apiKey) {
        return { valid: false, error: "API key is required" };
    }

    // Google API keys typically start with "AIza"
    if (!apiKey.startsWith("AIza")) {
        return {
            valid: false,
            error: "Invalid API key format. Google API keys typically start with 'AIza'",
        };
    }

    // In a real implementation, we might make a lightweight API call to validate the key
    // For now, we'll just check the format

    return { valid: true };
};

/**
 * Batch processes multiple image generation requests
 * @param requests Array of image generation requests
 * @returns Array of image generation responses
 */
export const batchGenerateImages = async (
    requests: ImageGenerationParams[],
): Promise<ImageGenerationResponse[]> => {
    // Process requests in sequence to avoid rate limiting
    const results: ImageGenerationResponse[] = [];

    for (const request of requests) {
        try {
            const result = await generateImageFromPrompt(request);
            results.push(result);

            // Add a small delay between requests to avoid rate limiting
            if (requests.length > 1) {
                await new Promise((resolve) => setTimeout(resolve, 1000));
            }
        } catch (error) {
            results.push({
                success: false,
                error: error instanceof Error ? error.message : String(error),
                errorType: ImageGenerationErrorType.UNKNOWN_ERROR,
                retryable: true,
            });
        }
    }

    return results;
};

/**
 * Retries an image generation request with exponential backoff
 * @param params Image generation parameters
 * @param maxRetries Maximum number of retries
 * @returns The image generation response
 */
export const retryImageGeneration = async (
    params: ImageGenerationParams,
    maxRetries: number = 3,
): Promise<ImageGenerationResponse> => {
    let lastError: ImageGenerationResponse | null = null;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
            // Wait with exponential backoff before retrying
            if (attempt > 0) {
                const delayMs = 2 ** attempt * 1000;
                await new Promise((resolve) => setTimeout(resolve, delayMs));
            }

            const result = await generateImageFromPrompt(params);

            // If successful, return the result
            if (result.success) {
                return result;
            }

            // If not retryable, return the error immediately
            if (!result.retryable) {
                return result;
            }

            // Store the error for potential return
            lastError = result;

            // Only retry rate limit and network errors
            if (
                result.errorType !== ImageGenerationErrorType.RATE_LIMIT &&
                result.errorType !== ImageGenerationErrorType.NETWORK_ERROR
            ) {
                return result;
            }

            log.info(`Retrying image generation (attempt ${attempt + 1}/${maxRetries})`, {
                model: params.model,
                errorType: result.errorType,
            });
        } catch (error) {
            lastError = {
                success: false,
                error: error instanceof Error ? error.message : String(error),
                errorType: ImageGenerationErrorType.UNKNOWN_ERROR,
                retryable: true,
            };
        }
    }

    // Return the last error if all retries failed
    return (
        lastError || {
            success: false,
            error: "Maximum retries exceeded",
            errorType: ImageGenerationErrorType.UNKNOWN_ERROR,
            retryable: false,
        }
    );
};
