import { ModelEnum } from "@repo/ai/models";
import { beforeEach, describe, expect, it, vi } from "vitest";
import {
    determineErrorType,
    generateImageFromPrompt,
    generateImageWithRetry,
    ImageGenerationErrorType,
    type ImageGenerationParams,
} from "../image-generation";

// Mock the AI SDK
vi.mock("ai", () => ({
    experimental_generateImage: vi.fn(),
}));

// Mock the providers
vi.mock("@repo/ai/providers", () => ({
    getImageModel: vi.fn(),
}));

// Mock the logger
vi.mock("@repo/shared/logger", () => ({
    log: {
        info: vi.fn(),
        error: vi.fn(),
    },
}));

describe("Image Generation Service", () => {
    const mockGenerateImage = vi.fn();
    const mockGetImageModel = vi.fn();

    beforeEach(() => {
        vi.clearAllMocks();

        // Setup default mocks
        const { experimental_generateImage } = require("ai");
        const { getImageModel } = require("@repo/ai/providers");

        experimental_generateImage.mockImplementation(mockGenerateImage);
        getImageModel.mockImplementation(mockGetImageModel);

        // Default successful response
        mockGetImageModel.mockReturnValue({
            provider: "google",
            model: "imagen-3.0-generate-001",
        });

        mockGenerateImage.mockResolvedValue({
            image: {
                uint8Array: new Uint8Array([1, 2, 3, 4]),
            },
        });
    });

    describe("generateImageFromPrompt", () => {
        const defaultParams: ImageGenerationParams = {
            prompt: "A beautiful sunset over mountains",
            model: ModelEnum.GEMINI_IMAGEN_3_0,
            aspectRatio: "1:1",
            byokKeys: { GEMINI_API_KEY: "test-key" },
            isVtPlus: false,
        };

        it("should generate image successfully with valid parameters", async () => {
            const result = await generateImageFromPrompt(defaultParams);

            expect(result.success).toBe(true);
            expect(result.imageData).toEqual(new Uint8Array([1, 2, 3, 4]));
            expect(result.metadata).toMatchObject({
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                prompt: "A beautiful sunset over mountains",
                aspectRatio: "1:1",
            });
        });

        it("should handle missing API key error", async () => {
            mockGenerateImage.mockRejectedValue(new Error("API key required"));

            const result = await generateImageFromPrompt({
                ...defaultParams,
                byokKeys: {},
            });

            expect(result.success).toBe(false);
            expect(result.errorType).toBe(ImageGenerationErrorType.API_KEY_MISSING);
            expect(result.retryable).toBe(false);
        });

        it("should handle invalid API key error", async () => {
            mockGenerateImage.mockRejectedValue(new Error("Invalid API key"));

            const result = await generateImageFromPrompt(defaultParams);

            expect(result.success).toBe(false);
            expect(result.errorType).toBe(ImageGenerationErrorType.API_KEY_INVALID);
            expect(result.retryable).toBe(false);
        });

        it("should handle content policy violation", async () => {
            mockGenerateImage.mockRejectedValue(new Error("Content policy violation"));

            const result = await generateImageFromPrompt(defaultParams);

            expect(result.success).toBe(false);
            expect(result.errorType).toBe(ImageGenerationErrorType.CONTENT_POLICY);
            expect(result.retryable).toBe(false);
        });

        it("should handle rate limit error", async () => {
            mockGenerateImage.mockRejectedValue(new Error("Rate limit exceeded"));

            const result = await generateImageFromPrompt(defaultParams);

            expect(result.success).toBe(false);
            expect(result.errorType).toBe(ImageGenerationErrorType.RATE_LIMIT);
            expect(result.retryable).toBe(true);
        });

        it("should handle quota exceeded error", async () => {
            mockGenerateImage.mockRejectedValue(new Error("Quota exceeded"));

            const result = await generateImageFromPrompt(defaultParams);

            expect(result.success).toBe(false);
            expect(result.errorType).toBe(ImageGenerationErrorType.QUOTA_EXCEEDED);
            expect(result.retryable).toBe(false);
        });

        it("should handle network error", async () => {
            mockGenerateImage.mockRejectedValue(new Error("Network error"));

            const result = await generateImageFromPrompt(defaultParams);

            expect(result.success).toBe(false);
            expect(result.errorType).toBe(ImageGenerationErrorType.NETWORK_ERROR);
            expect(result.retryable).toBe(true);
        });
    });

    describe("generateImageWithRetry", () => {
        const defaultParams: ImageGenerationParams = {
            prompt: "A beautiful sunset over mountains",
            model: ModelEnum.GEMINI_IMAGEN_3_0,
            aspectRatio: "1:1",
            byokKeys: { GEMINI_API_KEY: "test-key" },
            isVtPlus: false,
        };

        it("should succeed on first attempt", async () => {
            const result = await generateImageWithRetry(defaultParams, 3);

            expect(result.success).toBe(true);
            expect(mockGenerateImage).toHaveBeenCalledTimes(1);
        });

        it("should retry on rate limit error and succeed", async () => {
            mockGenerateImage
                .mockRejectedValueOnce(new Error("Rate limit exceeded"))
                .mockResolvedValueOnce({
                    image: {
                        uint8Array: new Uint8Array([1, 2, 3, 4]),
                    },
                });

            const result = await generateImageWithRetry(defaultParams, 3);

            expect(result.success).toBe(true);
            expect(mockGenerateImage).toHaveBeenCalledTimes(2);
        });

        it("should not retry on non-retryable errors", async () => {
            mockGenerateImage.mockRejectedValue(new Error("Invalid API key"));

            const result = await generateImageWithRetry(defaultParams, 3);

            expect(result.success).toBe(false);
            expect(result.errorType).toBe(ImageGenerationErrorType.API_KEY_INVALID);
            expect(mockGenerateImage).toHaveBeenCalledTimes(1);
        });

        it("should exhaust retries and return last error", async () => {
            mockGenerateImage.mockRejectedValue(new Error("Rate limit exceeded"));

            const result = await generateImageWithRetry(defaultParams, 2);

            expect(result.success).toBe(false);
            expect(result.errorType).toBe(ImageGenerationErrorType.RATE_LIMIT);
            expect(mockGenerateImage).toHaveBeenCalledTimes(2);
        });
    });

    describe("determineErrorType", () => {
        it("should identify API key missing errors", () => {
            expect(determineErrorType("API key required")).toBe(
                ImageGenerationErrorType.API_KEY_MISSING,
            );
            expect(determineErrorType("Missing API key")).toBe(
                ImageGenerationErrorType.API_KEY_MISSING,
            );
        });

        it("should identify invalid API key errors", () => {
            expect(determineErrorType("Invalid API key")).toBe(
                ImageGenerationErrorType.API_KEY_INVALID,
            );
            expect(determineErrorType("Unauthorized")).toBe(
                ImageGenerationErrorType.API_KEY_INVALID,
            );
            expect(determineErrorType("403")).toBe(ImageGenerationErrorType.API_KEY_INVALID);
        });

        it("should identify content policy violations", () => {
            expect(determineErrorType("Content policy violation")).toBe(
                ImageGenerationErrorType.CONTENT_POLICY,
            );
            expect(determineErrorType("Safety filter")).toBe(
                ImageGenerationErrorType.CONTENT_POLICY,
            );
        });

        it("should identify rate limit errors", () => {
            expect(determineErrorType("Rate limit exceeded")).toBe(
                ImageGenerationErrorType.RATE_LIMIT,
            );
            expect(determineErrorType("Too many requests")).toBe(
                ImageGenerationErrorType.RATE_LIMIT,
            );
        });

        it("should identify quota exceeded errors", () => {
            expect(determineErrorType("Quota exceeded")).toBe(
                ImageGenerationErrorType.QUOTA_EXCEEDED,
            );
            expect(determineErrorType("Billing")).toBe(ImageGenerationErrorType.QUOTA_EXCEEDED);
        });

        it("should identify network errors", () => {
            expect(determineErrorType("Network error")).toBe(
                ImageGenerationErrorType.NETWORK_ERROR,
            );
            expect(determineErrorType("Connection failed")).toBe(
                ImageGenerationErrorType.NETWORK_ERROR,
            );
        });

        it("should default to unknown error", () => {
            expect(determineErrorType("Some random error")).toBe(
                ImageGenerationErrorType.UNKNOWN_ERROR,
            );
        });
    });
});
