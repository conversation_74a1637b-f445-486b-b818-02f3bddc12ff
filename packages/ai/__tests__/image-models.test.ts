import { describe, expect, it } from "vitest";
import {
    ModelEnum,
    isImageModel,
    getImageModel,
    getImageModels,
    getModelFromChatMode,
    imageModels,
} from "../models";
import { ChatMode } from "@repo/shared/config";

describe("Image Model Configuration", () => {
    describe("ModelEnum", () => {
        it("should include image generation models", () => {
            expect(ModelEnum.GEMINI_IMAGEN_3_0).toBe("imagen-3.0-generate-001");
            expect(ModelEnum.GEMINI_IMAGEN_3_0_FAST).toBe("imagen-3.0-fast-generate-001");
        });
    });

    describe("imageModels array", () => {
        it("should contain properly configured image models", () => {
            expect(imageModels).toHaveLength(2);
            
            const imagen3 = imageModels.find(m => m.id === ModelEnum.GEMINI_IMAGEN_3_0);
            expect(imagen3).toBeDefined();
            expect(imagen3?.name).toBe("Gemini Imagen 3.0");
            expect(imagen3?.provider).toBe("google");
            expect(imagen3?.type).toBe("image");
            expect(imagen3?.maxPromptLength).toBe(2048);
            expect(imagen3?.aspectRatios).toContain("1:1");
            expect(imagen3?.aspectRatios).toContain("16:9");
            expect(imagen3?.aspectRatios).toContain("9:16");

            const imagenFast = imageModels.find(m => m.id === ModelEnum.GEMINI_IMAGEN_3_0_FAST);
            expect(imagenFast).toBeDefined();
            expect(imagenFast?.name).toBe("Gemini Imagen 3.0 Fast");
            expect(imagenFast?.provider).toBe("google");
            expect(imagenFast?.type).toBe("image");
            expect(imagenFast?.maxPromptLength).toBe(2048);
        });

        it("should have valid aspect ratios", () => {
            const validAspectRatios = ["1:1", "16:9", "9:16", "4:3", "3:4"];
            
            imageModels.forEach(model => {
                expect(model.aspectRatios).toBeDefined();
                model.aspectRatios.forEach(ratio => {
                    expect(validAspectRatios).toContain(ratio);
                });
            });
        });
    });

    describe("isImageModel", () => {
        it("should return true for image generation models", () => {
            expect(isImageModel(ModelEnum.GEMINI_IMAGEN_3_0)).toBe(true);
            expect(isImageModel(ModelEnum.GEMINI_IMAGEN_3_0_FAST)).toBe(true);
        });

        it("should return false for text generation models", () => {
            expect(isImageModel(ModelEnum.GEMINI_2_5_PRO)).toBe(false);
            expect(isImageModel(ModelEnum.GEMINI_2_5_FLASH)).toBe(false);
            expect(isImageModel(ModelEnum.GPT_4o)).toBe(false);
            expect(isImageModel(ModelEnum.CLAUDE_4_SONNET)).toBe(false);
        });
    });

    describe("getImageModel", () => {
        it("should return image model by ID", () => {
            const model = getImageModel(ModelEnum.GEMINI_IMAGEN_3_0);
            expect(model).toBeDefined();
            expect(model?.id).toBe(ModelEnum.GEMINI_IMAGEN_3_0);
            expect(model?.type).toBe("image");
        });

        it("should return undefined for non-image models", () => {
            const model = getImageModel(ModelEnum.GEMINI_2_5_PRO);
            expect(model).toBeUndefined();
        });
    });

    describe("getImageModels", () => {
        it("should return all image models", () => {
            const models = getImageModels();
            expect(models).toHaveLength(2);
            expect(models.every(m => m.type === "image")).toBe(true);
        });
    });

    describe("getModelFromChatMode", () => {
        it("should map image chat modes to correct models", () => {
            expect(getModelFromChatMode(ChatMode.GEMINI_IMAGEN_3_0)).toBe(ModelEnum.GEMINI_IMAGEN_3_0);
            expect(getModelFromChatMode(ChatMode.GEMINI_IMAGEN_3_0_FAST)).toBe(ModelEnum.GEMINI_IMAGEN_3_0_FAST);
        });

        it("should handle non-image chat modes", () => {
            expect(getModelFromChatMode(ChatMode.GEMINI_2_5_PRO)).toBe(ModelEnum.GEMINI_2_5_PRO);
            expect(getModelFromChatMode(ChatMode.GEMINI_2_5_FLASH)).toBe(ModelEnum.GEMINI_2_5_FLASH);
        });
    });

    describe("Image Model Properties", () => {
        it("should have consistent provider for all image models", () => {
            imageModels.forEach(model => {
                expect(model.provider).toBe("google");
            });
        });

        it("should have reasonable prompt length limits", () => {
            imageModels.forEach(model => {
                expect(model.maxPromptLength).toBeGreaterThan(0);
                expect(model.maxPromptLength).toBeLessThanOrEqual(4096);
            });
        });

        it("should have at least basic aspect ratios", () => {
            const requiredRatios = ["1:1"];
            
            imageModels.forEach(model => {
                requiredRatios.forEach(ratio => {
                    expect(model.aspectRatios).toContain(ratio);
                });
            });
        });

        it("should have unique IDs", () => {
            const ids = imageModels.map(m => m.id);
            const uniqueIds = new Set(ids);
            expect(uniqueIds.size).toBe(ids.length);
        });

        it("should have descriptive names", () => {
            imageModels.forEach(model => {
                expect(model.name).toBeTruthy();
                expect(model.name.length).toBeGreaterThan(5);
                expect(model.name).toContain("Imagen");
            });
        });
    });

    describe("Integration with ChatMode", () => {
        it("should have corresponding ChatMode values", () => {
            expect(ChatMode.GEMINI_IMAGEN_3_0).toBeDefined();
            expect(ChatMode.GEMINI_IMAGEN_3_0_FAST).toBeDefined();
        });

        it("should map correctly between ChatMode and ModelEnum", () => {
            const chatModeToModel = [
                [ChatMode.GEMINI_IMAGEN_3_0, ModelEnum.GEMINI_IMAGEN_3_0],
                [ChatMode.GEMINI_IMAGEN_3_0_FAST, ModelEnum.GEMINI_IMAGEN_3_0_FAST],
            ] as const;

            chatModeToModel.forEach(([chatMode, expectedModel]) => {
                const actualModel = getModelFromChatMode(chatMode);
                expect(actualModel).toBe(expectedModel);
            });
        });
    });

    describe("Model Validation", () => {
        it("should have valid model structure", () => {
            imageModels.forEach(model => {
                // Required fields
                expect(model.id).toBeTruthy();
                expect(model.name).toBeTruthy();
                expect(model.provider).toBeTruthy();
                expect(model.type).toBe("image");
                expect(model.maxPromptLength).toBeGreaterThan(0);
                expect(Array.isArray(model.aspectRatios)).toBe(true);
                expect(model.aspectRatios.length).toBeGreaterThan(0);

                // Optional fields should be defined if present
                if (model.description) {
                    expect(typeof model.description).toBe("string");
                }
                if (model.maxImageSize) {
                    expect(typeof model.maxImageSize).toBe("number");
                    expect(model.maxImageSize).toBeGreaterThan(0);
                }
            });
        });

        it("should have consistent naming convention", () => {
            imageModels.forEach(model => {
                // Model IDs should follow pattern
                expect(model.id).toMatch(/^imagen-\d+\.\d+(-fast)?-generate-\d+$/);
                
                // Names should be descriptive
                expect(model.name).toMatch(/^Gemini Imagen \d+\.\d+( Fast)?$/);
            });
        });
    });
});
