import { ModelEnum } from "@repo/ai/models";
import { ChatMode } from "@repo/shared/config";
import { describe, expect, it, jest, beforeEach, afterEach } from "vitest";
import { generateImageFromPrompt, generateImageWithRetry } from "../services/image-generation";
import { storeGeneratedImage, getGeneratedImage, deleteImagesForThread } from "@repo/common/lib/storage/image-storage";
import { imagePerformanceMonitor } from "@repo/common/lib/performance/image-performance-monitor";

// Mock the AI SDK
jest.mock("ai", () => ({
    generateImage: jest.fn(),
}));

// Mock the providers
jest.mock("../providers", () => ({
    getImageModel: jest.fn(),
}));

// Mock logger
jest.mock("@repo/shared/logger", () => ({
    log: {
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
    },
}));

// Mock IndexedDB for storage tests
jest.mock("dexie", () => {
    const mockTable = {
        add: jest.fn(),
        get: jest.fn(),
        where: jest.fn(() => ({
            equals: jest.fn(() => ({
                toArray: jest.fn(),
                delete: jest.fn(),
            })),
        })),
        clear: jest.fn(),
    };

    const mockDatabase = {
        images: mockTable,
        version: jest.fn(() => ({
            stores: jest.fn(),
        })),
    };

    return {
        default: jest.fn(() => mockDatabase),
    };
});

describe("Image Generation End-to-End Tests", () => {
    const mockGenerateImage = jest.fn();
    const mockGetImageModel = jest.fn();
    const mockImageData = new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);

    beforeEach(() => {
        jest.clearAllMocks();
        
        const { generateImage } = require("ai");
        const { getImageModel } = require("../providers");
        
        generateImage.mockImplementation(mockGenerateImage);
        getImageModel.mockImplementation(mockGetImageModel);
        
        // Default successful setup
        mockGetImageModel.mockReturnValue({
            provider: "google",
            model: "imagen-3.0-generate-001",
        });
        
        mockGenerateImage.mockResolvedValue({
            image: {
                uint8Array: mockImageData,
                mimeType: "image/png",
                width: 512,
                height: 512,
            },
        });

        // Mock storage
        const Dexie = require("dexie").default;
        const mockDb = new Dexie();
        mockDb.images.add.mockResolvedValue("img_123");
        mockDb.images.get.mockResolvedValue({
            id: "img_123",
            threadId: "thread_123",
            messageId: "msg_123",
            imageData: mockImageData,
            prompt: "Test image",
            model: ModelEnum.GEMINI_IMAGEN_3_0,
            size: mockImageData.length,
            createdAt: new Date(),
            updatedAt: new Date(),
        });
    });

    afterEach(() => {
        // Clear performance monitoring history
        imagePerformanceMonitor.clearHistory();
    });

    describe("Complete Image Generation Workflow", () => {
        it("should complete full end-to-end image generation and storage workflow", async () => {
            const threadId = "thread_123";
            const messageId = "msg_123";
            const prompt = "A beautiful sunset over mountains";

            // Step 1: Generate image
            const generationResult = await generateImageFromPrompt({
                prompt,
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                aspectRatio: "1:1",
                byokKeys: { GEMINI_API_KEY: "test-key" },
                isVtPlus: false,
                enableCompression: true,
                targetSizeKB: 100,
            });

            // Verify generation success
            expect(generationResult.success).toBe(true);
            expect(generationResult.imageData).toBeDefined();
            expect(generationResult.metadata).toMatchObject({
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                prompt,
                aspectRatio: "1:1",
            });

            // Step 2: Store generated image
            const imageId = await storeGeneratedImage({
                threadId,
                messageId,
                imageData: generationResult.imageData!,
                prompt,
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                aspectRatio: "1:1",
                mimeType: "image/png",
            });

            expect(imageId).toBeTruthy();

            // Step 3: Retrieve stored image
            const retrievedImage = await getGeneratedImage(imageId!);
            expect(retrievedImage).toBeDefined();
            expect(retrievedImage?.prompt).toBe(prompt);
            expect(retrievedImage?.model).toBe(ModelEnum.GEMINI_IMAGEN_3_0);

            // Step 4: Verify performance monitoring
            const stats = imagePerformanceMonitor.getPerformanceStats();
            expect(stats.totalOperations).toBe(1);
            expect(stats.successRate).toBe(100);
        });

        it("should handle complete error workflow with retry and monitoring", async () => {
            // Setup to fail first attempt, succeed on retry
            mockGenerateImage
                .mockRejectedValueOnce(new Error("Rate limit exceeded"))
                .mockResolvedValueOnce({
                    image: {
                        uint8Array: mockImageData,
                        mimeType: "image/png",
                        width: 512,
                        height: 512,
                    },
                });

            const result = await generateImageWithRetry({
                prompt: "Test image",
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                aspectRatio: "1:1",
                byokKeys: { GEMINI_API_KEY: "test-key" },
                isVtPlus: false,
            }, 3);

            // Should succeed after retry
            expect(result.success).toBe(true);
            expect(mockGenerateImage).toHaveBeenCalledTimes(2);

            // Verify performance monitoring tracked the retry
            const stats = imagePerformanceMonitor.getPerformanceStats();
            expect(stats.totalOperations).toBe(1);
            expect(stats.successRate).toBe(100);
        });

        it("should handle conversation cleanup workflow", async () => {
            const threadId = "thread_cleanup_test";
            
            // Generate and store multiple images for a thread
            for (let i = 0; i < 3; i++) {
                const result = await generateImageFromPrompt({
                    prompt: `Test image ${i}`,
                    model: ModelEnum.GEMINI_IMAGEN_3_0,
                    aspectRatio: "1:1",
                    byokKeys: { GEMINI_API_KEY: "test-key" },
                    isVtPlus: false,
                });

                await storeGeneratedImage({
                    threadId,
                    messageId: `msg_${i}`,
                    imageData: result.imageData!,
                    prompt: `Test image ${i}`,
                    model: ModelEnum.GEMINI_IMAGEN_3_0,
                });
            }

            // Clean up thread images
            const deletedCount = await deleteImagesForThread(threadId);
            expect(deletedCount).toBeGreaterThan(0);
        });
    });

    describe("Performance and Monitoring Integration", () => {
        it("should track performance metrics across multiple operations", async () => {
            const operations = [
                { model: ModelEnum.GEMINI_IMAGEN_3_0, prompt: "Test 1" },
                { model: ModelEnum.GEMINI_IMAGEN_3_0_FAST, prompt: "Test 2" },
                { model: ModelEnum.GEMINI_IMAGEN_3_0, prompt: "Test 3" },
            ];

            // Execute multiple operations
            for (const op of operations) {
                await generateImageFromPrompt({
                    prompt: op.prompt,
                    model: op.model,
                    aspectRatio: "1:1",
                    byokKeys: { GEMINI_API_KEY: "test-key" },
                    isVtPlus: false,
                });
            }

            // Verify performance statistics
            const stats = imagePerformanceMonitor.getPerformanceStats();
            expect(stats.totalOperations).toBe(3);
            expect(stats.successRate).toBe(100);
            expect(stats.modelPerformance).toHaveProperty(ModelEnum.GEMINI_IMAGEN_3_0);
            expect(stats.modelPerformance).toHaveProperty(ModelEnum.GEMINI_IMAGEN_3_0_FAST);
        });

        it("should track compression performance", async () => {
            const result = await generateImageFromPrompt({
                prompt: "Large test image",
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                aspectRatio: "1:1",
                byokKeys: { GEMINI_API_KEY: "test-key" },
                isVtPlus: false,
                enableCompression: true,
                targetSizeKB: 50,
            });

            expect(result.success).toBe(true);
            expect(result.metadata?.compressionRatio).toBeDefined();
            expect(result.metadata?.originalSize).toBeDefined();

            const stats = imagePerformanceMonitor.getPerformanceStats();
            expect(stats.averageCompressionRatio).toBeGreaterThan(0);
        });
    });

    describe("Error Handling Integration", () => {
        it("should handle and track various error scenarios", async () => {
            const errorScenarios = [
                { error: "Invalid API key", expectedType: "API_KEY_INVALID" },
                { error: "Rate limit exceeded", expectedType: "RATE_LIMIT" },
                { error: "Content policy violation", expectedType: "CONTENT_POLICY" },
            ];

            for (const scenario of errorScenarios) {
                mockGenerateImage.mockRejectedValueOnce(new Error(scenario.error));

                const result = await generateImageFromPrompt({
                    prompt: "Test error handling",
                    model: ModelEnum.GEMINI_IMAGEN_3_0,
                    aspectRatio: "1:1",
                    byokKeys: { GEMINI_API_KEY: "test-key" },
                    isVtPlus: false,
                });

                expect(result.success).toBe(false);
                expect(result.error).toBeTruthy();
            }

            // Verify error tracking
            const stats = imagePerformanceMonitor.getPerformanceStats();
            expect(stats.totalOperations).toBe(3);
            expect(stats.successRate).toBe(0);
            expect(Object.keys(stats.errorBreakdown).length).toBeGreaterThan(0);
        });
    });

    describe("Model Configuration Integration", () => {
        it("should work with all configured image models", async () => {
            const imageModels = [ModelEnum.GEMINI_IMAGEN_3_0, ModelEnum.GEMINI_IMAGEN_3_0_FAST];

            for (const model of imageModels) {
                const result = await generateImageFromPrompt({
                    prompt: `Test with ${model}`,
                    model,
                    aspectRatio: "16:9",
                    byokKeys: { GEMINI_API_KEY: "test-key" },
                    isVtPlus: false,
                });

                expect(result.success).toBe(true);
                expect(result.metadata?.model).toBe(model);
            }

            const stats = imagePerformanceMonitor.getPerformanceStats();
            expect(stats.totalOperations).toBe(2);
            expect(Object.keys(stats.modelPerformance)).toHaveLength(2);
        });

        it("should validate aspect ratios for different models", async () => {
            const aspectRatios = ["1:1", "16:9", "9:16", "4:3"];

            for (const aspectRatio of aspectRatios) {
                const result = await generateImageFromPrompt({
                    prompt: `Test aspect ratio ${aspectRatio}`,
                    model: ModelEnum.GEMINI_IMAGEN_3_0,
                    aspectRatio,
                    byokKeys: { GEMINI_API_KEY: "test-key" },
                    isVtPlus: false,
                });

                expect(result.success).toBe(true);
                expect(result.metadata?.aspectRatio).toBe(aspectRatio);
            }
        });
    });

    describe("Progress Tracking Integration", () => {
        it("should provide progress updates throughout generation", async () => {
            const progressUpdates: Array<{ stage: string; progress: number; message: string }> = [];

            await generateImageFromPrompt({
                prompt: "Test progress tracking",
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                aspectRatio: "1:1",
                byokKeys: { GEMINI_API_KEY: "test-key" },
                isVtPlus: false,
                progressCallback: (progress) => {
                    progressUpdates.push({
                        stage: progress.stage,
                        progress: progress.progress,
                        message: progress.message,
                    });
                },
            });

            // Should have received multiple progress updates
            expect(progressUpdates.length).toBeGreaterThan(3);
            
            // Should include key stages
            const stages = progressUpdates.map(u => u.stage);
            expect(stages).toContain('initializing');
            expect(stages).toContain('generating');
            expect(stages).toContain('complete');
            
            // Progress should generally increase
            const progressValues = progressUpdates.map(u => u.progress);
            expect(progressValues[progressValues.length - 1]).toBe(100);
        });
    });
});
