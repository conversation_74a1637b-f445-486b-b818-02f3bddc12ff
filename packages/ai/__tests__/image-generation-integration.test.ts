import { ModelEnum } from "@repo/ai/models";
import { ChatMode } from "@repo/shared/config";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { isImageModel } from "../models";
import { getImageModel } from "../providers";
import { generateImageFromPrompt } from "../services/image-generation";

// Mock the AI SDK
vi.mock("ai", () => ({
    experimental_generateImage: vi.fn(),
}));

// Mock the providers
vi.mock("../providers", () => ({
    getImageModel: vi.fn(),
}));

// Mock logger
vi.mock("@repo/shared/logger", () => ({
    log: {
        info: vi.fn(),
        error: vi.fn(),
    },
}));

describe("Image Generation Integration", () => {
    const mockGenerateImage = vi.fn();
    const mockGetImageModel = vi.fn();

    beforeEach(() => {
        vi.clearAllMocks();

        const { experimental_generateImage } = require("ai");
        experimental_generateImage.mockImplementation(mockGenerateImage);

        (getImageModel as any).mockImplementation(mockGetImageModel);

        // Default successful setup
        mockGetImageModel.mockReturnValue({
            provider: "google",
            model: "imagen-3.0-generate-001",
        });

        mockGenerateImage.mockResolvedValue({
            image: {
                uint8Array: new Uint8Array([1, 2, 3, 4, 5]),
            },
        });
    });

    describe("End-to-End Image Generation Flow", () => {
        it("should complete full image generation workflow", async () => {
            // 1. Verify model configuration
            expect(isImageModel(ModelEnum.GEMINI_IMAGEN_3_0)).toBe(true);
            expect(ChatMode.GEMINI_IMAGEN_3_0).toBeDefined();

            // 2. Generate image
            const result = await generateImageFromPrompt({
                prompt: "A beautiful sunset over mountains",
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                aspectRatio: "1:1",
                byokKeys: { GEMINI_API_KEY: "test-key" },
                isVtPlus: false,
            });

            // 3. Verify successful generation
            expect(result.success).toBe(true);
            expect(result.imageData).toEqual(new Uint8Array([1, 2, 3, 4, 5]));
            expect(result.metadata).toMatchObject({
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                prompt: "A beautiful sunset over mountains",
                aspectRatio: "1:1",
            });

            // 4. Verify provider was called correctly
            expect(mockGetImageModel).toHaveBeenCalledWith(
                ModelEnum.GEMINI_IMAGEN_3_0,
                { GEMINI_API_KEY: "test-key" },
                false,
            );

            // 5. Verify AI SDK was called correctly
            expect(mockGenerateImage).toHaveBeenCalledWith({
                model: expect.any(Object),
                prompt: "A beautiful sunset over mountains",
                aspectRatio: "1:1",
            });
        });

        it("should handle different aspect ratios", async () => {
            const aspectRatios = ["1:1", "16:9", "9:16", "4:3", "3:4"];

            for (const aspectRatio of aspectRatios) {
                const result = await generateImageFromPrompt({
                    prompt: "Test image",
                    model: ModelEnum.GEMINI_IMAGEN_3_0,
                    aspectRatio,
                    byokKeys: { GEMINI_API_KEY: "test-key" },
                    isVtPlus: false,
                });

                expect(result.success).toBe(true);
                expect(result.metadata?.aspectRatio).toBe(aspectRatio);
            }
        });

        it("should work with both image models", async () => {
            const models = [ModelEnum.GEMINI_IMAGEN_3_0, ModelEnum.GEMINI_IMAGEN_3_0_FAST];

            for (const model of models) {
                const result = await generateImageFromPrompt({
                    prompt: "Test image",
                    model,
                    aspectRatio: "1:1",
                    byokKeys: { GEMINI_API_KEY: "test-key" },
                    isVtPlus: false,
                });

                expect(result.success).toBe(true);
                expect(result.metadata?.model).toBe(model);
            }
        });
    });

    describe("Error Handling Integration", () => {
        it("should handle API key validation errors", async () => {
            mockGenerateImage.mockRejectedValue(new Error("Invalid API key"));

            const result = await generateImageFromPrompt({
                prompt: "Test image",
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                aspectRatio: "1:1",
                byokKeys: { GEMINI_API_KEY: "invalid-key" },
                isVtPlus: false,
            });

            expect(result.success).toBe(false);
            expect(result.error).toContain("API key");
            expect(result.retryable).toBe(false);
        });

        it("should handle content policy violations", async () => {
            mockGenerateImage.mockRejectedValue(new Error("Content policy violation"));

            const result = await generateImageFromPrompt({
                prompt: "Inappropriate content",
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                aspectRatio: "1:1",
                byokKeys: { GEMINI_API_KEY: "test-key" },
                isVtPlus: false,
            });

            expect(result.success).toBe(false);
            expect(result.error).toContain("content policies");
            expect(result.retryable).toBe(false);
        });

        it("should handle rate limiting with retry capability", async () => {
            mockGenerateImage.mockRejectedValue(new Error("Rate limit exceeded"));

            const result = await generateImageFromPrompt({
                prompt: "Test image",
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                aspectRatio: "1:1",
                byokKeys: { GEMINI_API_KEY: "test-key" },
                isVtPlus: false,
            });

            expect(result.success).toBe(false);
            expect(result.retryable).toBe(true);
        });
    });

    describe("Provider Integration", () => {
        it("should call provider with correct parameters", async () => {
            await generateImageFromPrompt({
                prompt: "Test image",
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                aspectRatio: "16:9",
                byokKeys: { GEMINI_API_KEY: "test-key" },
                isVtPlus: true,
            });

            expect(mockGetImageModel).toHaveBeenCalledWith(
                ModelEnum.GEMINI_IMAGEN_3_0,
                { GEMINI_API_KEY: "test-key" },
                true,
            );
        });

        it("should handle provider errors gracefully", async () => {
            mockGetImageModel.mockImplementation(() => {
                throw new Error("Provider configuration error");
            });

            const result = await generateImageFromPrompt({
                prompt: "Test image",
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                aspectRatio: "1:1",
                byokKeys: { GEMINI_API_KEY: "test-key" },
                isVtPlus: false,
            });

            expect(result.success).toBe(false);
            expect(result.error).toBeTruthy();
        });
    });

    describe("Model Configuration Integration", () => {
        it("should validate image model configuration", () => {
            // Test that image models are properly configured
            expect(isImageModel(ModelEnum.GEMINI_IMAGEN_3_0)).toBe(true);
            expect(isImageModel(ModelEnum.GEMINI_IMAGEN_3_0_FAST)).toBe(true);

            // Test that non-image models are not identified as image models
            expect(isImageModel(ModelEnum.GEMINI_2_5_PRO)).toBe(false);
            expect(isImageModel(ModelEnum.GPT_4o)).toBe(false);
        });

        it("should have corresponding ChatMode values", () => {
            expect(ChatMode.GEMINI_IMAGEN_3_0).toBe("gemini-imagen-3.0");
            expect(ChatMode.GEMINI_IMAGEN_3_0_FAST).toBe("gemini-imagen-3.0-fast");
        });
    });

    describe("Response Processing", () => {
        it("should process successful response correctly", async () => {
            const mockImageData = new Uint8Array([10, 20, 30, 40, 50]);
            mockGenerateImage.mockResolvedValue({
                image: {
                    uint8Array: mockImageData,
                },
            });

            const result = await generateImageFromPrompt({
                prompt: "Test image",
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                aspectRatio: "1:1",
                byokKeys: { GEMINI_API_KEY: "test-key" },
                isVtPlus: false,
            });

            expect(result.success).toBe(true);
            expect(result.imageData).toEqual(mockImageData);
            expect(result.metadata).toMatchObject({
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                prompt: "Test image",
                aspectRatio: "1:1",
                size: mockImageData.length,
            });
            expect(result.metadata?.generatedAt).toBeInstanceOf(Date);
        });

        it("should handle missing image data in response", async () => {
            mockGenerateImage.mockResolvedValue({
                image: {
                    // Missing uint8Array
                },
            });

            const result = await generateImageFromPrompt({
                prompt: "Test image",
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                aspectRatio: "1:1",
                byokKeys: { GEMINI_API_KEY: "test-key" },
                isVtPlus: false,
            });

            expect(result.success).toBe(false);
            expect(result.error).toBeTruthy();
        });
    });
});
