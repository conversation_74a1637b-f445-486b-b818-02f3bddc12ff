import { ModelEnum } from "@repo/ai/models";
import { ChatMode } from "@repo/shared/config";
import { beforeEach, describe, expect, it, jest } from "vitest";
import { getImageModel, isImageModel } from "../models";
import { generateImageFromPrompt } from "../services/image-generation";

// Mock the AI SDK
jest.mock("ai", () => ({
    generateImage: jest.fn(),
}));

// Mock the providers
jest.mock("../providers", () => ({
    getImageModel: jest.fn(),
}));

// Mock logger
jest.mock("@repo/shared/logger", () => ({
    log: {
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
    },
}));

describe("OpenAI Image Generation Integration", () => {
    const mockGenerateImage = jest.fn();
    const mockGetImageModelProvider = jest.fn();
    const mockImageData = new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);

    beforeEach(() => {
        jest.clearAllMocks();

        const { generateImage } = require("ai");
        const { getImageModel: getImageModelProvider } = require("../providers");

        generateImage.mockImplementation(mockGenerateImage);
        getImageModelProvider.mockImplementation(mockGetImageModelProvider);

        // Default successful setup for OpenAI
        mockGetImageModelProvider.mockReturnValue({
            provider: "openai",
            model: "dall-e-3",
        });

        mockGenerateImage.mockResolvedValue({
            image: {
                uint8Array: mockImageData,
                mimeType: "image/png",
                width: 1024,
                height: 1024,
            },
        });
    });

    describe("OpenAI Model Configuration", () => {
        it("should recognize DALL-E 3 as an image model", () => {
            expect(isImageModel(ModelEnum.OPENAI_DALL_E_3)).toBe(true);
        });

        it("should recognize DALL-E 2 as an image model", () => {
            expect(isImageModel(ModelEnum.OPENAI_DALL_E_2)).toBe(true);
        });

        it("should recognize ChatGPT Image-1 as an image model", () => {
            expect(isImageModel(ModelEnum.OPENAI_CHATGPT_IMAGE_1)).toBe(true);
        });

        it("should get DALL-E 3 model configuration", () => {
            const model = getImageModel(ModelEnum.OPENAI_DALL_E_3);

            expect(model).toBeDefined();
            expect(model?.name).toBe("DALL-E 3");
            expect(model?.provider).toBe("openai");
            expect(model?.type).toBe("image");
            expect(model?.maxPromptLength).toBe(4000);
            expect(model?.aspectRatios).toContain("1:1");
            expect(model?.aspectRatios).toContain("16:9");
            expect(model?.aspectRatios).toContain("9:16");
        });

        it("should get DALL-E 2 model configuration", () => {
            const model = getImageModel(ModelEnum.OPENAI_DALL_E_2);

            expect(model).toBeDefined();
            expect(model?.name).toBe("DALL-E 2");
            expect(model?.provider).toBe("openai");
            expect(model?.type).toBe("image");
            expect(model?.maxPromptLength).toBe(1000);
            expect(model?.aspectRatios).toEqual(["1:1"]);
        });

        it("should get ChatGPT Image-1 model configuration", () => {
            const model = getImageModel(ModelEnum.OPENAI_CHATGPT_IMAGE_1);

            expect(model).toBeDefined();
            expect(model?.name).toBe("ChatGPT Image-1");
            expect(model?.provider).toBe("openai");
            expect(model?.type).toBe("image");
            expect(model?.maxPromptLength).toBe(4000);
            expect(model?.aspectRatios).toContain("1:1");
            expect(model?.aspectRatios).toContain("16:9");
            expect(model?.aspectRatios).toContain("9:16");
        });
    });

    describe("OpenAI Image Generation", () => {
        it("should generate image with DALL-E 3", async () => {
            const result = await generateImageFromPrompt({
                prompt: "A futuristic cityscape at sunset",
                model: ModelEnum.OPENAI_DALL_E_3,
                aspectRatio: "16:9",
                byokKeys: { OPENAI_API_KEY: "test-key" },
                isVtPlus: false,
            });

            expect(result.success).toBe(true);
            expect(result.imageData).toBeDefined();
            expect(result.metadata).toMatchObject({
                model: ModelEnum.OPENAI_DALL_E_3,
                prompt: "A futuristic cityscape at sunset",
                aspectRatio: "16:9",
            });

            // Verify the correct provider was called
            expect(mockGetImageModelProvider).toHaveBeenCalledWith(
                ModelEnum.OPENAI_DALL_E_3,
                { OPENAI_API_KEY: "test-key" },
                false,
            );

            // Verify the AI SDK was called with correct parameters
            expect(mockGenerateImage).toHaveBeenCalledWith({
                model: expect.objectContaining({
                    provider: "openai",
                    model: "dall-e-3",
                }),
                prompt: "A futuristic cityscape at sunset",
                aspectRatio: "16:9",
            });
        });

        it("should generate image with DALL-E 2", async () => {
            // Update mock for DALL-E 2
            mockGetImageModelProvider.mockReturnValue({
                provider: "openai",
                model: "dall-e-2",
            });

            const result = await generateImageFromPrompt({
                prompt: "A beautiful landscape",
                model: ModelEnum.OPENAI_DALL_E_2,
                aspectRatio: "1:1",
                byokKeys: { OPENAI_API_KEY: "test-key" },
                isVtPlus: false,
            });

            expect(result.success).toBe(true);
            expect(result.imageData).toBeDefined();
            expect(result.metadata).toMatchObject({
                model: ModelEnum.OPENAI_DALL_E_2,
                prompt: "A beautiful landscape",
                aspectRatio: "1:1",
            });

            // Verify the correct model was used
            expect(mockGenerateImage).toHaveBeenCalledWith({
                model: expect.objectContaining({
                    provider: "openai",
                    model: "dall-e-2",
                }),
                prompt: "A beautiful landscape",
                aspectRatio: "1:1",
            });
        });

        it("should generate image with ChatGPT Image-1", async () => {
            // Update mock for ChatGPT Image-1
            mockGetImageModelProvider.mockReturnValue({
                provider: "openai",
                model: "chatgpt-image-1",
            });

            const result = await generateImageFromPrompt({
                prompt: "A vibrant digital artwork of a cyberpunk cityscape",
                model: ModelEnum.OPENAI_CHATGPT_IMAGE_1,
                aspectRatio: "16:9",
                byokKeys: { OPENAI_API_KEY: "test-key" },
                isVtPlus: false,
            });

            expect(result.success).toBe(true);
            expect(result.imageData).toBeDefined();
            expect(result.metadata).toMatchObject({
                model: ModelEnum.OPENAI_CHATGPT_IMAGE_1,
                prompt: "A vibrant digital artwork of a cyberpunk cityscape",
                aspectRatio: "16:9",
            });

            // Verify the correct model was used
            expect(mockGenerateImage).toHaveBeenCalledWith({
                model: expect.objectContaining({
                    provider: "openai",
                    model: "chatgpt-image-1",
                }),
                prompt: "A vibrant digital artwork of a cyberpunk cityscape",
                aspectRatio: "16:9",
            });
        });

        it("should handle OpenAI API errors appropriately", async () => {
            mockGenerateImage.mockRejectedValue(new Error("Invalid API key"));

            const result = await generateImageFromPrompt({
                prompt: "Test prompt",
                model: ModelEnum.OPENAI_DALL_E_3,
                aspectRatio: "1:1",
                byokKeys: { OPENAI_API_KEY: "invalid-key" },
                isVtPlus: false,
            });

            expect(result.success).toBe(false);
            expect(result.error).toContain("Invalid API key");
        });

        it("should respect DALL-E 3 prompt length limits", async () => {
            const longPrompt = "A".repeat(5000); // Exceeds 4000 character limit

            const result = await generateImageFromPrompt({
                prompt: longPrompt,
                model: ModelEnum.OPENAI_DALL_E_3,
                aspectRatio: "1:1",
                byokKeys: { OPENAI_API_KEY: "test-key" },
                isVtPlus: false,
            });

            // Should fail validation due to prompt length
            expect(result.success).toBe(false);
            expect(result.error).toContain("prompt");
        });

        it("should respect DALL-E 2 prompt length limits", async () => {
            const longPrompt = "A".repeat(1500); // Exceeds 1000 character limit

            const result = await generateImageFromPrompt({
                prompt: longPrompt,
                model: ModelEnum.OPENAI_DALL_E_2,
                aspectRatio: "1:1",
                byokKeys: { OPENAI_API_KEY: "test-key" },
                isVtPlus: false,
            });

            // Should fail validation due to prompt length
            expect(result.success).toBe(false);
            expect(result.error).toContain("prompt");
        });

        it("should validate aspect ratios for DALL-E models", async () => {
            // DALL-E 2 only supports 1:1
            const result = await generateImageFromPrompt({
                prompt: "Test image",
                model: ModelEnum.OPENAI_DALL_E_2,
                aspectRatio: "16:9", // Not supported by DALL-E 2
                byokKeys: { OPENAI_API_KEY: "test-key" },
                isVtPlus: false,
            });

            // Should fail validation due to unsupported aspect ratio
            expect(result.success).toBe(false);
            expect(result.error).toContain("aspect ratio");
        });
    });

    describe("OpenAI vs Gemini Comparison", () => {
        it("should handle both OpenAI and Gemini models in the same workflow", async () => {
            // Test DALL-E 3
            let result = await generateImageFromPrompt({
                prompt: "A modern office space",
                model: ModelEnum.OPENAI_DALL_E_3,
                aspectRatio: "16:9",
                byokKeys: { OPENAI_API_KEY: "test-key" },
                isVtPlus: false,
            });

            expect(result.success).toBe(true);
            expect(result.metadata?.model).toBe(ModelEnum.OPENAI_DALL_E_3);

            // Update mock for Gemini
            mockGetImageModelProvider.mockReturnValue({
                provider: "google",
                model: "imagen-3.0-generate-001",
            });

            // Test Gemini Imagen
            result = await generateImageFromPrompt({
                prompt: "A modern office space",
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                aspectRatio: "16:9",
                byokKeys: { GEMINI_API_KEY: "test-key" },
                isVtPlus: false,
            });

            expect(result.success).toBe(true);
            expect(result.metadata?.model).toBe(ModelEnum.GEMINI_IMAGEN_3_0);
        });

        it("should show different capabilities between models", () => {
            const dalleModel = getImageModel(ModelEnum.OPENAI_DALL_E_3);
            const geminiModel = getImageModel(ModelEnum.GEMINI_IMAGEN_3_0);

            // DALL-E 3 has higher prompt limit
            expect(dalleModel?.maxPromptLength).toBe(4000);
            expect(geminiModel?.maxPromptLength).toBe(2048);

            // Gemini supports more aspect ratios
            expect(geminiModel?.aspectRatios).toHaveLength(5);
            expect(dalleModel?.aspectRatios).toHaveLength(3);

            // Both support common aspect ratios
            expect(dalleModel?.aspectRatios).toContain("1:1");
            expect(geminiModel?.aspectRatios).toContain("1:1");
        });
    });

    describe("Chat Mode Integration", () => {
        it("should map OpenAI chat modes to correct models", () => {
            const { getModelFromChatMode } = require("../models");

            expect(getModelFromChatMode(ChatMode.OPENAI_DALL_E_3)).toBe(ModelEnum.OPENAI_DALL_E_3);
            expect(getModelFromChatMode(ChatMode.OPENAI_DALL_E_2)).toBe(ModelEnum.OPENAI_DALL_E_2);
            expect(getModelFromChatMode(ChatMode.OPENAI_CHATGPT_IMAGE_1)).toBe(
                ModelEnum.OPENAI_CHATGPT_IMAGE_1,
            );
        });

        it("should provide correct chat mode names", () => {
            const { getChatModeName } = require("@repo/shared/config");

            expect(getChatModeName(ChatMode.OPENAI_DALL_E_3)).toBe("OpenAI DALL-E 3");
            expect(getChatModeName(ChatMode.OPENAI_DALL_E_2)).toBe("OpenAI DALL-E 2");
            expect(getChatModeName(ChatMode.OPENAI_CHATGPT_IMAGE_1)).toBe("OpenAI ChatGPT Image-1");
        });
    });
});
