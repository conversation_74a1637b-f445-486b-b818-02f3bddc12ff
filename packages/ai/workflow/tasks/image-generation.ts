import { getModelFromChatMode, isImageModel } from "@repo/ai/models";
import { generateImageFromPrompt } from "@repo/ai/services/image-generation";
import { createTask } from "@repo/orchestrator";
import { log } from "@repo/shared/logger";
import type { WorkflowContextSchema, WorkflowEventSchema } from "../flow";
import { handleError, sendEvents } from "../utils";

export const imageGenerationTask = createTask<WorkflowEventSchema, WorkflowContextSchema>({
    name: "image-generation",
    execute: async ({ events, context, signal }) => {
        const question = context?.get("question") || "";
        const mode = context?.get("mode") || "";
        const apiKeys = context?.get("apiKeys") || {};
        const userTier = context?.get("userTier");
        const { updateAnswer, updateStatus } = sendEvents(events);

        log.info("=== imageGenerationTask START ===");
        log.info("Image generation request:", {
            prompt: question.slice(0, 100),
            mode,
            hasApiKeys: !!apiKeys,
            userTier,
        });

        try {
            updateStatus("PENDING");

            // Get the image model from the chat mode
            const model = getModelFromChatMode(mode);
            
            if (!model || !isImageModel(model)) {
                throw new Error(`Invalid image model for mode: ${mode}`);
            }

            log.info("Using image model:", { model });

            // Update status to generating
            updateAnswer({
                text: "🎨 Generating image...",
                finalText: "",
                status: "PENDING",
            });

            // Generate the image
            const result = await generateImageFromPrompt({
                prompt: question,
                model,
                aspectRatio: "1:1", // Default aspect ratio, could be made configurable
                byokKeys: apiKeys,
                isVtPlus: userTier === "PLUS",
                abortSignal: signal,
                progressCallback: (progress) => {
                    updateAnswer({
                        text: `🎨 Generating image... ${Math.round(progress)}%`,
                        finalText: "",
                        status: "PENDING",
                    });
                },
            });

            if (!result.success || !result.imageData) {
                throw new Error(result.error || "Image generation failed");
            }

            log.info("Image generation successful:", {
                model,
                imageSize: result.imageData.length,
                metadata: result.metadata,
            });

            // Create the final response with the image
            const imageResponse = {
                type: "image",
                imageData: result.imageData,
                metadata: result.metadata,
                prompt: question,
            };

            updateAnswer({
                text: "",
                finalText: JSON.stringify(imageResponse),
                status: "COMPLETED",
            });

            return imageResponse;
        } catch (error: any) {
            log.error("Image generation failed:", { error: error.message });
            
            updateAnswer({
                text: "",
                finalText: `❌ Image generation failed: ${error.message}`,
                status: "ERROR",
            });

            throw error;
        }
    },
    onError: handleError,
});
