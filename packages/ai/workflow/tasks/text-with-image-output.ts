import { getModelFromChatMode, supportsImageOutput } from "@repo/ai/models";
import { createTask } from "@repo/orchestrator";
import { log } from "@repo/shared/logger";
import type { WorkflowContextSchema, WorkflowEventSchema } from "../flow";
import { handleError, sendEvents } from "../utils";

export const textWithImageOutputTask = createTask<WorkflowEventSchema, WorkflowContextSchema>({
    name: "text-with-image-output",
    execute: async ({ events, context, signal }) => {
        const question = context?.get("question") || "";
        const mode = context?.get("mode") || "";
        const apiKeys = context?.get("apiKeys") || {};
        const userTier = context?.get("userTier");
        const messages = context?.get("messages") || [];
        const { updateAnswer, updateStatus } = sendEvents(events);

        log.info("=== textWithImageOutputTask START ===");
        log.info("Text with image output request:", {
            prompt: question.slice(0, 100),
            mode,
            hasApiKeys: !!apiKeys,
            userTier,
        });

        try {
            updateStatus("PENDING");

            // Get the model from the chat mode
            const model = getModelFromChatMode(mode);

            if (!model || !supportsImageOutput(model)) {
                throw new Error(`Invalid image output model for mode: ${mode}`);
            }

            log.info("Using text model with image output:", { model });

            // Update status to generating
            updateAnswer({
                text: "🎨 Generating response with images...",
                finalText: "",
                status: "PENDING",
            });

            // Generate text with image output using responseModalities
            // We need to use the AI SDK directly for this since generateText doesn't support providerOptions parameter
            const { generateText: generateTextAI } = await import("ai");
            const { getLanguageModel } = await import("@repo/ai/providers");

            const selectedModel = getLanguageModel(
                model,
                undefined, // middleware
                apiKeys,
                false, // useSearchGrounding
                undefined, // geo
                false, // claude4InterleavedThinking
                userTier === "PLUS",
            );

            const result = await generateTextAI({
                model: selectedModel,
                prompt: question,
                messages,
                abortSignal: signal,
                providerOptions: {
                    google: {
                        responseModalities: ["TEXT", "IMAGE"],
                    },
                },
                onChunk: ({ chunk }) => {
                    if (chunk.type === "text-delta") {
                        updateAnswer({
                            text: chunk.textDelta,
                            finalText: "",
                            status: "PENDING",
                        });
                    }
                },
            });

            log.info("Text with image output generation successful:", {
                model,
                textLength: result.text.length,
                hasFiles: !!result.files?.length,
            });

            // Check if the result has files (images)
            if (result.files && result.files.length > 0) {
                log.info("Found generated files:", {
                    fileCount: result.files.length,
                    fileTypes: result.files.map((f) => f.mimeType),
                });

                // Process the files and create a response that includes both text and images
                const imageFiles = result.files.filter((file) =>
                    file.mimeType.startsWith("image/"),
                );

                if (imageFiles.length > 0) {
                    // Create a response that includes both text and image data
                    const response = {
                        type: "text-with-images",
                        text: result.text,
                        images: imageFiles.map((file) => ({
                            data: file.data, // Base64 image data
                            mimeType: file.mimeType,
                            name: file.name || "generated-image",
                        })),
                        metadata: {
                            model,
                            prompt: question,
                            imageCount: imageFiles.length,
                        },
                    };

                    updateAnswer({
                        text: "",
                        finalText: JSON.stringify(response),
                        status: "COMPLETED",
                    });

                    return response;
                }
            }

            // If no images were generated, return just the text
            updateAnswer({
                text: "",
                finalText: result.text,
                status: "COMPLETED",
            });

            return {
                type: "text",
                text: result.text,
                metadata: {
                    model,
                    prompt: question,
                },
            };
        } catch (error: any) {
            log.error("Text with image output generation failed:", { error: error.message });

            updateAnswer({
                text: "",
                finalText: `❌ Generation failed: ${error.message}`,
                status: "ERROR",
            });

            throw error;
        }
    },
    onError: handleError,
});
