{"name": "@repo/ai", "version": "1.0.0", "description": "AI integration package with multi-provider support and workflow orchestration", "private": true, "author": "<PERSON><PERSON>", "license": "MIT", "scripts": {"lint": "ox<PERSON>", "type-check": "tsc --noEmit"}, "exports": {"./main": "./main.ts", "./models": "./models.ts", "./providers": "./providers.ts", "./services/api-key-mapper": "./services/api-key-mapper.ts", "./services/error-messages": "./services/error-messages.ts", "./services/image-generation": "./services/image-generation.ts", "./tools": "./tools/index.ts", "./workflow": "./workflow/flow.ts", "./workflow/tasks/chat-mode-router": "./workflow/tasks/chat-mode-router.ts", "./worker": "./worker/index.ts", "./constants/reasoning": "./constants/reasoning.ts", "./cache": "./cache/gemini-cache.ts"}, "dependencies": {"@ai-sdk/anthropic": "^1.1.8", "@ai-sdk/fireworks": "^0.1.16", "@ai-sdk/google": "^1.1.16", "@ai-sdk/openai": "^1.1.11", "@ai-sdk/openai-compatible": "^0.2.15", "@ai-sdk/provider": "^0.0.26", "@ai-sdk/togetherai": "^0.1.11", "@ai-sdk/xai": "^1.2.16", "@anthropic-ai/sdk": "^0.54.0", "@google/generative-ai": "^0.24.1", "@openrouter/ai-sdk-provider": "^0.0.5", "@repo/orchestrator": "*", "@repo/shared": "*", "@types/node": "^20.11.5", "ai": "^4.3.16", "braintrust": "^0.0.206", "dotenv": "^16.5.0", "js-tiktoken": "^1.0.20", "json-schema-to-zod": "^2.6.1", "openai": "^5.5.1", "together-ai": "^0.16.0", "uuid": "^11.1.0", "zod": "^3.23.8", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@repo/typescript-config": "*", "typescript": "^5.3.3"}}