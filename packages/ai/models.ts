import { ChatMode } from "@repo/shared/config";
import type { CoreMessage } from "ai";
import { ReasoningTagName, ReasoningType } from "./constants/reasoning";
import type { ProviderEnumType } from "./providers";

export const ModelEnum = {
    CLAUDE_4_SONNET: "claude-4-sonnet-********",
    GEMINI_2_5_FLASH_LITE: "gemini-2.5-flash-lite-preview-06-17",
    GEMINI_2_5_FLASH: "gemini-2.5-flash",
    GEMINI_2_5_PRO: "gemini-2.5-pro",
    CLAUDE_4_OPUS: "claude-4-opus-********",
    GPT_4o_Mini: "gpt-4o-mini",
    GPT_4o: "gpt-4o",
    GPT_4_1_Mini: "gpt-4.1-mini",
    GPT_4_1_Nano: "gpt-4.1-nano",
    GPT_4_1: "gpt-4.1",
    O3: "o3",
    O3_<PERSON>: "o3-mini",
    O4_Mini: "o4-mini",
    GROK_3: "grok-3",
    GROK_3_MINI: "grok-3-mini",
    GROK_4: "grok-4",
    // Fireworks models
    DEEPSEEK_R1_FIREWORKS: "accounts/fireworks/models/deepseek-r1-0528",
    KIMI_K2_INSTRUCT_FIREWORKS: "accounts/fireworks/models/kimi-k2-instruct",
    // OpenRouter models
    DEEPSEEK_V3_0324: "deepseek/deepseek-chat-v3-0324",
    DEEPSEEK_R1: "deepseek/deepseek-r1",
    QWEN3_235B_A22B: "qwen/qwen3-235b-a22b",
    QWEN3_32B: "qwen/qwen3-32b",
    MISTRAL_NEMO: "mistralai/mistral-nemo",
    QWEN3_14B: "qwen/qwen3-14b",
    KIMI_K2: "moonshot/kimi-k2",
    // Gemini Image Generation models
    GEMINI_IMAGEN_3_0: "imagen-3.0-generate-001",
    GEMINI_IMAGEN_3_0_FAST: "imagen-3.0-fast-generate-001",
    // OpenAI Image Generation models
    OPENAI_DALL_E_3: "dall-e-3",
    OPENAI_DALL_E_2: "dall-e-2",
} as const;

export type ModelEnum = (typeof ModelEnum)[keyof typeof ModelEnum];

export type Model = {
    id: ModelEnum;
    name: string;
    provider: ProviderEnumType;
    maxTokens: number;
    contextWindow: number;
    isFree?: boolean; // Add flag for free models
};

export type ImageModel = {
    id: ModelEnum;
    name: string;
    provider: ProviderEnumType;
    type: "image";
    maxPromptLength: number;
    aspectRatios: string[];
    isFree?: boolean;
};

export const models: Model[] = [
    {
        id: ModelEnum.GPT_4o,
        name: "GPT-4o",
        provider: "openai",
        maxTokens: 16_384,
        contextWindow: 128_000,
    },
    {
        id: ModelEnum.GPT_4_1_Mini,
        name: "GPT-4.1 Mini",
        provider: "openai",
        maxTokens: 32_768,
        contextWindow: 1_047_576,
    },
    {
        id: ModelEnum.GPT_4_1,
        name: "GPT-4.1",
        provider: "openai",
        maxTokens: 32_768,
        contextWindow: 1_047_576,
    },
    {
        id: ModelEnum.O3,
        name: "o3",
        provider: "openai",
        maxTokens: 100_000,
        contextWindow: 200_000,
    },
    {
        id: ModelEnum.O3_Mini,
        name: "o3-mini",
        provider: "openai",
        maxTokens: 100_000,
        contextWindow: 200_000,
    },
    {
        id: ModelEnum.O4_Mini,
        name: "o4 mini",
        provider: "openai",
        maxTokens: 100_000,
        contextWindow: 200_000,
    },
    {
        id: ModelEnum.GPT_4o_Mini,
        name: "GPT-4o Mini",
        provider: "openai",
        maxTokens: 100_000,
        contextWindow: 200_000,
    },
    {
        id: ModelEnum.CLAUDE_4_SONNET,
        name: "Claude 4 Sonnet",
        provider: "anthropic",
        maxTokens: 64_000,
        contextWindow: 200_000,
    },
    {
        id: ModelEnum.CLAUDE_4_OPUS,
        name: "Claude 4 Opus",
        provider: "anthropic",
        maxTokens: 32_000,
        contextWindow: 200_000,
    },
    {
        id: ModelEnum.GEMINI_2_5_FLASH,
        name: "Gemini 2.5 Flash",
        provider: "google",
        maxTokens: 1_048_576,
        contextWindow: 1_048_576,
    },
    {
        id: ModelEnum.GEMINI_2_5_FLASH_LITE,
        name: "Gemini 2.5 Flash Lite Preview 06-17",
        provider: "google",
        maxTokens: 65_536,
        contextWindow: 65_536,
        isFree: true,
    },
    {
        id: ModelEnum.GEMINI_2_5_PRO,
        name: "Gemini 2.5 Pro",
        provider: "google",
        maxTokens: 1_048_576,
        contextWindow: 1_048_576,
    },
    {
        id: ModelEnum.GROK_3,
        name: "Grok 3",
        provider: "xai",
        maxTokens: 131_072,
        contextWindow: 131_072,
    },
    {
        id: ModelEnum.GROK_3_MINI,
        name: "Grok 3 Mini",
        provider: "xai",
        maxTokens: 131_072,
        contextWindow: 131_072,
    },
    {
        id: ModelEnum.GROK_4,
        name: "Grok 4",
        provider: "xai",
        maxTokens: 256_000,
        contextWindow: 256_000,
    },
    // Fireworks models
    {
        id: ModelEnum.DEEPSEEK_R1_FIREWORKS,
        name: "DeepSeek R1 (Fireworks)",
        provider: "fireworks",
        maxTokens: 32_768,
        contextWindow: 163_840,
    },
    {
        id: ModelEnum.KIMI_K2_INSTRUCT_FIREWORKS,
        name: "Kimi K2 Instruct (Fireworks)",
        provider: "fireworks",
        maxTokens: 4_096,
        contextWindow: 131_072,
    },
    // OpenRouter models
    {
        id: ModelEnum.DEEPSEEK_V3_0324,
        name: "DeepSeek V3 0324",
        provider: "openrouter",
        maxTokens: 32_768,
        contextWindow: 163_840,
    },
    {
        id: ModelEnum.QWEN3_235B_A22B,
        name: "Qwen3 235B A22B",
        provider: "openrouter",
        maxTokens: 8192,
        contextWindow: 40_960,
    },
    {
        id: ModelEnum.QWEN3_32B,
        name: "Qwen3 32B",
        provider: "openrouter",
        maxTokens: 8192,
        contextWindow: 40_960,
    },
    {
        id: ModelEnum.MISTRAL_NEMO,
        name: "Mistral Nemo",
        provider: "openrouter",
        maxTokens: 32_768,
        contextWindow: 131_072,
    },
    {
        id: ModelEnum.QWEN3_14B,
        name: "Qwen3 14B",
        provider: "openrouter",
        maxTokens: 8192,
        contextWindow: 40_960,
        isFree: true,
    },
    {
        id: ModelEnum.KIMI_K2,
        name: "Kimi K2",
        provider: "openrouter",
        maxTokens: 4096,
        contextWindow: 131_072,
    },
];

export const imageModels: ImageModel[] = [
    {
        id: ModelEnum.GEMINI_IMAGEN_3_0,
        name: "Gemini Imagen 3.0",
        provider: "google",
        type: "image",
        maxPromptLength: 2048,
        aspectRatios: ["1:1", "16:9", "9:16", "4:3", "3:4"],
        isFree: false,
    },
    {
        id: ModelEnum.GEMINI_IMAGEN_3_0_FAST,
        name: "Gemini Imagen 3.0 Fast",
        provider: "google",
        type: "image",
        maxPromptLength: 2048,
        aspectRatios: ["1:1", "16:9", "9:16", "4:3", "3:4"],
        isFree: false,
    },
    {
        id: ModelEnum.OPENAI_DALL_E_3,
        name: "DALL-E 3",
        provider: "openai",
        type: "image",
        maxPromptLength: 4000,
        aspectRatios: ["1:1", "16:9", "9:16"],
        isFree: false,
    },
    {
        id: ModelEnum.OPENAI_DALL_E_2,
        name: "DALL-E 2",
        provider: "openai",
        type: "image",
        maxPromptLength: 1000,
        aspectRatios: ["1:1"],
        isFree: false,
    },
];

export const getModelFromChatMode = (mode?: string): ModelEnum => {
    switch (mode) {
        case ChatMode.Deep:
            return ModelEnum.GEMINI_2_5_PRO;
        case ChatMode.Pro:
            return ModelEnum.GEMINI_2_5_FLASH;
        case ChatMode.GEMINI_2_5_FLASH_LITE:
            return ModelEnum.GEMINI_2_5_FLASH_LITE;
        case ChatMode.GEMINI_2_5_PRO:
            return ModelEnum.GEMINI_2_5_PRO;
        case ChatMode.DEEPSEEK_R1_FIREWORKS:
            return ModelEnum.DEEPSEEK_R1_FIREWORKS;
        case ChatMode.KIMI_K2_INSTRUCT_FIREWORKS:
            return ModelEnum.KIMI_K2_INSTRUCT_FIREWORKS;
        case ChatMode.DEEPSEEK_R1:
            return ModelEnum.DEEPSEEK_R1;
        case ChatMode.CLAUDE_4_SONNET:
            return ModelEnum.CLAUDE_4_SONNET;
        case ChatMode.CLAUDE_4_OPUS:
            return ModelEnum.CLAUDE_4_OPUS;
        case ChatMode.GPT_4o_Mini:
            return ModelEnum.GPT_4o_Mini;
        case ChatMode.GPT_4o:
            return ModelEnum.GPT_4o;
        case ChatMode.GPT_4_1:
            return ModelEnum.GPT_4_1;
        case ChatMode.GPT_4_1_Mini:
            return ModelEnum.GPT_4_1_Mini;
        case ChatMode.GPT_4_1_Nano:
            return ModelEnum.GPT_4_1_Nano;
        case ChatMode.O3:
            return ModelEnum.O3;
        case ChatMode.O3_Mini:
            return ModelEnum.O3_Mini;
        case ChatMode.O4_Mini:
            return ModelEnum.O4_Mini;
        case ChatMode.GROK_3:
            return ModelEnum.GROK_3;
        case ChatMode.GROK_3_MINI:
            return ModelEnum.GROK_3_MINI;
        case ChatMode.GROK_4:
            return ModelEnum.GROK_4;
        // OpenRouter models
        case ChatMode.DEEPSEEK_V3_0324:
            return ModelEnum.DEEPSEEK_V3_0324;
        case ChatMode.QWEN3_235B_A22B:
            return ModelEnum.QWEN3_235B_A22B;
        case ChatMode.QWEN3_32B:
            return ModelEnum.QWEN3_32B;
        case ChatMode.MISTRAL_NEMO:
            return ModelEnum.MISTRAL_NEMO;
        case ChatMode.QWEN3_14B:
            return ModelEnum.QWEN3_14B;
        case ChatMode.KIMI_K2:
            return ModelEnum.KIMI_K2;
        // Image generation models
        case ChatMode.GEMINI_IMAGEN_3_0:
            return ModelEnum.GEMINI_IMAGEN_3_0;
        case ChatMode.GEMINI_IMAGEN_3_0_FAST:
            return ModelEnum.GEMINI_IMAGEN_3_0_FAST;
        case ChatMode.OPENAI_DALL_E_3:
            return ModelEnum.OPENAI_DALL_E_3;
        case ChatMode.OPENAI_DALL_E_2:
            return ModelEnum.OPENAI_DALL_E_2;
        default:
            return ModelEnum.GEMINI_2_5_FLASH_LITE;
    }
};

export const getChatModeMaxTokens = (mode: ChatMode) => {
    switch (mode) {
        case ChatMode.Pro:
        case ChatMode.Deep:
        case ChatMode.GEMINI_2_5_PRO:
        case ChatMode.GEMINI_2_5_FLASH:
        case ChatMode.GEMINI_2_5_FLASH_LITE:
            return 1_048_576;
        case ChatMode.CLAUDE_4_SONNET:
        case ChatMode.CLAUDE_4_OPUS:
            return 200_000;
        case ChatMode.O3:
        case ChatMode.O3_Mini:
        case ChatMode.O4_Mini:
        case ChatMode.GPT_4o_Mini:
            return 200_000;
        case ChatMode.GPT_4o:
            return 128_000;
        case ChatMode.GPT_4_1_Mini:
        case ChatMode.GPT_4_1:
            return 1_047_576;
        case ChatMode.GROK_3:
        case ChatMode.GROK_3_MINI:
        case ChatMode.GROK_4:
            return 131_072;
        // OpenRouter models
        case ChatMode.DEEPSEEK_V3_0324:
        case ChatMode.DEEPSEEK_R1:
            return 163_840;
        case ChatMode.QWEN3_235B_A22B:
        case ChatMode.QWEN3_32B:
        case ChatMode.QWEN3_14B:
            return 40_960;
        case ChatMode.MISTRAL_NEMO:
        case ChatMode.KIMI_K2:
            return 131_072;
        default:
            return 100_000;
    }
};

export const estimateTokensByWordCount = (text: string): number => {
    // Simple word splitting by whitespace
    const words = text?.trim().split(/\s+/);

    // Using a multiplier of 1.35 tokens per word for English text
    const estimatedTokens = Math.ceil(words.length * 1.35);

    return estimatedTokens;
};

export const estimateTokensForMessages = (messages: CoreMessage[]): number => {
    let totalTokens = 0;

    for (const message of messages) {
        if (typeof message.content === "string") {
            totalTokens += estimateTokensByWordCount(message.content);
        } else if (Array.isArray(message.content)) {
            for (const part of message.content) {
                if (part.type === "text") {
                    totalTokens += estimateTokensByWordCount(part.text);
                }
            }
        }
    }

    return totalTokens;
};

export const supportsNativeWebSearch = (model: ModelEnum): boolean => {
    const googleModels = [
        ModelEnum.GEMINI_2_5_FLASH,
        ModelEnum.GEMINI_2_5_FLASH_LITE,
        ModelEnum.GEMINI_2_5_PRO,
    ];

    return googleModels.includes(model);
};

export const supportsOpenAIWebSearch = (model: ModelEnum): boolean => {
    const openaiWebSearchModels = [
        ModelEnum.GPT_4o_Mini,
        ModelEnum.GPT_4o,
        ModelEnum.O3,
        ModelEnum.O3_Mini,
        // Add other models as they become available for Responses API
    ];

    return openaiWebSearchModels.includes(model);
};

export const trimMessageHistoryEstimated = (messages: CoreMessage[], chatMode: ChatMode) => {
    const maxTokens = getChatModeMaxTokens(chatMode);
    let trimmedMessages = [...messages];

    if (trimmedMessages.length <= 1) {
        const tokenCount = estimateTokensForMessages(trimmedMessages);
        return { trimmedMessages, tokenCount };
    }

    const latestMessage = trimmedMessages.pop()!;

    const messageSizes = trimmedMessages.map((msg) => {
        const tokens =
            typeof msg.content === "string"
                ? estimateTokensByWordCount(msg.content)
                : Array.isArray(msg.content)
                  ? msg.content.reduce(
                        (sum, part) =>
                            part.type === "text" ? sum + estimateTokensByWordCount(part.text) : sum,
                        0,
                    )
                  : 0;
        return { message: msg, tokens };
    });

    let totalTokens = messageSizes.reduce((sum, item) => sum + item.tokens, 0);

    // Count tokens for the latest message
    const latestMessageTokens =
        typeof latestMessage.content === "string"
            ? estimateTokensByWordCount(latestMessage.content)
            : Array.isArray(latestMessage.content)
              ? latestMessage.content.reduce(
                    (sum, part) =>
                        part.type === "text" ? sum + estimateTokensByWordCount(part.text) : sum,
                    0,
                )
              : 0;

    totalTokens += latestMessageTokens;

    while (totalTokens > maxTokens && messageSizes.length > 0) {
        const removed = messageSizes.shift();
        if (removed) {
            totalTokens -= removed.tokens;
        }
    }

    trimmedMessages = messageSizes.map((item) => item.message);
    trimmedMessages.push(latestMessage);

    return { trimmedMessages, tokenCount: totalTokens };
};

/**
 * Detects if a model supports reasoning tokens/thinking capabilities
 */
export const supportsReasoning = (model: ModelEnum): boolean => {
    // DeepSeek reasoning models (via Fireworks, OpenRouter)
    const deepseekReasoningModels = [
        ModelEnum.DEEPSEEK_R1_FIREWORKS, // Fireworks
        ModelEnum.DEEPSEEK_R1, // OpenRouter
    ];

    // Anthropic reasoning models
    const anthropicReasoningModels = [
        ModelEnum.CLAUDE_4_SONNET, // claude-4-sonnet-********
        ModelEnum.CLAUDE_4_OPUS, // claude-4-opus-********
    ];

    // Gemini thinking models (existing functionality)
    const geminiThinkingModels = [
        ModelEnum.GEMINI_2_5_FLASH,
        ModelEnum.GEMINI_2_5_PRO,
        ModelEnum.GEMINI_2_5_FLASH_LITE,
    ];

    // OpenAI reasoning models
    const openaiReasoningModels = [ModelEnum.O3, ModelEnum.O3_Mini, ModelEnum.O4_Mini];

    return [
        ...deepseekReasoningModels,
        ...anthropicReasoningModels,
        ...geminiThinkingModels,
        ...openaiReasoningModels,
    ].includes(model);
};

/**
 * Checks if a model supports tool calls/function calling
 * Based on capabilities defined in models-data.json
 */
export const supportsTools = (model: ModelEnum): boolean => {
    // OpenAI models that support tools
    const openaiToolModels = [
        ModelEnum.GPT_4o,
        ModelEnum.GPT_4o_Mini,
        ModelEnum.GPT_4_1,
        ModelEnum.GPT_4_1_Mini,
        ModelEnum.GPT_4_1_Nano,
        // Note: O1/O3 models do NOT support tools
    ];

    // Anthropic models that support tools
    const anthropicToolModels = [ModelEnum.CLAUDE_4_SONNET, ModelEnum.CLAUDE_4_OPUS];

    // Google models that support tools
    const googleToolModels = [
        ModelEnum.GEMINI_2_5_FLASH,
        ModelEnum.GEMINI_2_5_PRO,
        ModelEnum.GEMINI_2_5_FLASH_LITE,
    ];

    // OpenRouter models that support tools
    const openrouterToolModels = [
        ModelEnum.DEEPSEEK_V3_0324,
        ModelEnum.QWEN3_235B_A22B,
        ModelEnum.QWEN3_32B,
        ModelEnum.QWEN3_14B,
        ModelEnum.MISTRAL_NEMO,
        ModelEnum.KIMI_K2,
    ];

    // xAI models that support tools
    const xaiToolModels = [ModelEnum.GROK_3, ModelEnum.GROK_3_MINI, ModelEnum.GROK_4];

    return [
        ...openaiToolModels,
        ...anthropicToolModels,
        ...googleToolModels,
        ...openrouterToolModels,
        ...xaiToolModels,
    ].includes(model);
};

/**
 * Checks if a model supports web search
 * Most models support web search through our implementation
 */
export const supportsWebSearch = (model: ModelEnum): boolean => {
    // Almost all models support web search through our unified implementation
    // Only some very specialized or limited models might not support it
    const nonWebSearchModels: ModelEnum[] = [
        // Add any models that don't support web search here if needed
    ];

    return !nonWebSearchModels.includes(model);
};

/**
 * Determines the reasoning implementation type for a model
 */
export const getReasoningType = (model: ModelEnum): ReasoningType => {
    // Gemini models use thinking config
    const geminiThinkingModels = [
        ModelEnum.GEMINI_2_5_FLASH,
        ModelEnum.GEMINI_2_5_PRO,
        ModelEnum.GEMINI_2_5_FLASH_LITE,
    ];

    if (geminiThinkingModels.includes(model)) {
        return ReasoningType.GEMINI_THINKING;
    }

    // DeepSeek models use reasoning middleware with <think> tags
    const deepseekReasoningModels = [ModelEnum.DEEPSEEK_R1_FIREWORKS, ModelEnum.DEEPSEEK_R1];

    if (deepseekReasoningModels.includes(model)) {
        return ReasoningType.DEEPSEEK_REASONING;
    }

    // Anthropic models support reasoning with providerOptions
    const anthropicReasoningModels = [ModelEnum.CLAUDE_4_SONNET, ModelEnum.CLAUDE_4_OPUS];

    if (anthropicReasoningModels.includes(model)) {
        return ReasoningType.ANTHROPIC_REASONING;
    }

    return ReasoningType.NONE;
};

/**
 * Gets the appropriate middleware tag for reasoning extraction
 */
export const getReasoningTagName = (model: ModelEnum): string | null => {
    const reasoningType = getReasoningType(model);

    switch (reasoningType) {
        case ReasoningType.DEEPSEEK_REASONING:
            return ReasoningTagName.THINK; // DeepSeek uses <think> tags
        case ReasoningType.ANTHROPIC_REASONING:
            return ReasoningTagName.THINKING; // Anthropic models may use different tags
        default:
            return null; // Gemini uses built-in thinking config, no middleware needed
    }
};

/**
 * Checks if a model is an image generation model
 */
export const isImageModel = (model: ModelEnum): boolean => {
    const imageModelIds = [
        ModelEnum.GEMINI_IMAGEN_3_0,
        ModelEnum.GEMINI_IMAGEN_3_0_FAST,
        ModelEnum.OPENAI_DALL_E_3,
        ModelEnum.OPENAI_DALL_E_2,
    ];

    return imageModelIds.includes(model);
};

/**
 * Gets an image model by its ID
 */
export const getImageModel = (model: ModelEnum): ImageModel | undefined => {
    return imageModels.find((m) => m.id === model);
};

/**
 * Gets all available image models
 */
export const getImageModels = (): ImageModel[] => {
    return imageModels;
};
