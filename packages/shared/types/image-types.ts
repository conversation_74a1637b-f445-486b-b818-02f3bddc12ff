import type { ModelEnum } from "../../ai/models";
import type { ThreadItem } from "../types";

/**
 * Interface for image generation request parameters
 */
export interface ImageGenerationRequest {
    prompt: string;
    model: ModelEnum;
    aspectRatio?: "1:1" | "16:9" | "9:16" | "4:3" | "3:4";
    apiKey: string;
}

/**
 * Interface for image generation response
 */
export interface ImageGenerationResponse {
    success: boolean;
    imageData?: Uint8Array;
    imageUrl?: string;
    error?: string;
    metadata?: {
        model: string;
        prompt: string;
        aspectRatio: string;
        generatedAt: Date;
    };
}

/**
 * Interface for image record in IndexedDB storage
 */
export interface ImageRecord {
    id: string;
    threadId: string;
    messageId: string;
    imageData: Uint8Array;
    prompt: string;
    model: string;
    aspectRatio?: string;
    createdAt: Date;
}

/**
 * Type guard to check if a ThreadItem is an image message
 */
export function isImageMessage(message: ThreadItem): boolean {
    return message.messageType === "image" && !!message.imageContent;
}

/**
 * Helper function to create an image message from generation response
 */
export function createImageMessage(
    threadId: string,
    response: ImageGenerationResponse,
    model: ModelEnum,
    prompt: string,
    aspectRatio?: string,
): Partial<ThreadItem> {
    return {
        threadId,
        messageType: "image",
        query: prompt,
        imageContent: {
            imageData: response.imageData!,
            imageUrl: response.imageUrl,
            prompt,
            model,
            aspectRatio,
        },
        metadata: {
            imageGeneration: {
                prompt,
                model,
                aspectRatio,
                generatedAt: new Date(),
            },
        },
    };
}
