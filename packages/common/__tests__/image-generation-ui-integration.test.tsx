import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it, jest, beforeEach } from "vitest";
import { ImageGenerationInput } from "../components/chat-input/ImageGenerationInput";
import { ImageModelSelector } from "../components/chat-input/ImageModelSelector";
import { ImagePreview } from "../components/chat/ImagePreview";
import { ModelEnum } from "@repo/ai/models";
import { ChatMode } from "@repo/shared/config";
import type { ImageContent } from "@repo/shared/types";

// Mock the stores
jest.mock("@repo/common/store", () => ({
    useChatStore: jest.fn(() => ({
        chatMode: ChatMode.GEMINI_IMAGEN_3_0,
        setChatMode: jest.fn(),
    })),
    useApiKeysStore: jest.fn(() => ({
        hasApiKeyForChatMode: jest.fn(() => true),
    })),
}));

// Mock the session
jest.mock("@repo/shared/lib/auth-client", () => ({
    useSession: jest.fn(() => ({
        data: { user: { id: "test-user" } },
    })),
}));

// Mock the subscription access
jest.mock("@repo/common/hooks/use-subscription-access", () => ({
    useSubscriptionAccess: jest.fn(() => ({
        canAccess: jest.fn(() => true),
    })),
}));

// Mock the models
jest.mock("@repo/ai/models", () => ({
    getImageModel: jest.fn(() => ({
        name: "Imagen 3.0",
        maxPromptLength: 2048,
        aspectRatios: ["1:1", "16:9", "9:16"],
        isFree: false,
    })),
    getImageModels: jest.fn(() => [
        {
            name: "Imagen 3.0",
            enum: "GEMINI_IMAGEN_3_0",
            maxPromptLength: 2048,
            aspectRatios: ["1:1", "16:9", "9:16"],
            isFree: false,
        },
        {
            name: "Imagen 3.0 Fast",
            enum: "GEMINI_IMAGEN_3_0_FAST",
            maxPromptLength: 1024,
            aspectRatios: ["1:1", "16:9"],
            isFree: true,
        },
    ]),
    ModelEnum,
}));

// Mock the accessibility components
jest.mock("../components/accessibility/image-accessibility", () => ({
    ImageAccessibilityControls: ({ onAltTextChange }: any) => (
        <div data-testid="accessibility-controls">
            <button onClick={() => onAltTextChange?.("Generated alt text")}>
                Generate Alt Text
            </button>
        </div>
    ),
    useImageAccessibility: jest.fn(() => ({
        altText: "Test alt text",
        setAltText: jest.fn(),
        generateImageAltText: jest.fn(),
    })),
}));

describe("Image Generation UI Integration Tests", () => {
    const mockOnGenerate = jest.fn();
    const mockOnRetry = jest.fn();
    const mockOnModelSelect = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("ImageGenerationInput Integration", () => {
        it("should render and handle complete input workflow", async () => {
            const user = userEvent.setup();
            
            render(
                <ImageGenerationInput
                    onGenerate={mockOnGenerate}
                    isGenerating={false}
                />
            );

            // Check initial render
            expect(screen.getByText("Image Generation")).toBeInTheDocument();
            expect(screen.getByPlaceholderText("Describe the image you want to generate...")).toBeInTheDocument();
            expect(screen.getByText("Generate")).toBeInTheDocument();

            // Test prompt input
            const promptInput = screen.getByPlaceholderText("Describe the image you want to generate...");
            await user.type(promptInput, "A beautiful sunset over mountains");

            // Test aspect ratio selection
            const aspectRatioSelect = screen.getByRole("combobox");
            await user.click(aspectRatioSelect);
            
            // Should show aspect ratio options
            await waitFor(() => {
                expect(screen.getByText("16:9")).toBeInTheDocument();
            });
            
            await user.click(screen.getByText("16:9"));

            // Test generation trigger
            const generateButton = screen.getByText("Generate");
            await user.click(generateButton);

            expect(mockOnGenerate).toHaveBeenCalledWith(
                "A beautiful sunset over mountains",
                "16:9"
            );
        });

        it("should show character count and validation", async () => {
            const user = userEvent.setup();
            
            render(
                <ImageGenerationInput
                    onGenerate={mockOnGenerate}
                    isGenerating={false}
                />
            );

            const promptInput = screen.getByPlaceholderText("Describe the image you want to generate...");
            
            // Type a long prompt
            const longPrompt = "A".repeat(100);
            await user.type(promptInput, longPrompt);

            // Should show character count
            expect(screen.getByText("100/2048 characters")).toBeInTheDocument();

            // Generate button should be enabled for valid input
            const generateButton = screen.getByText("Generate");
            expect(generateButton).not.toBeDisabled();
        });

        it("should handle generating state", () => {
            render(
                <ImageGenerationInput
                    onGenerate={mockOnGenerate}
                    isGenerating={true}
                />
            );

            // Should show generating state
            expect(screen.getByText("Generating...")).toBeInTheDocument();
            
            // Generate button should be disabled
            const generateButton = screen.getByText("Generating...");
            expect(generateButton).toBeDisabled();
        });

        it("should support keyboard navigation", async () => {
            const user = userEvent.setup();
            
            render(
                <ImageGenerationInput
                    onGenerate={mockOnGenerate}
                    isGenerating={false}
                />
            );

            const promptInput = screen.getByPlaceholderText("Describe the image you want to generate...");
            await user.type(promptInput, "Test prompt");

            // Test Enter key to generate
            await user.keyboard("{Enter}");
            
            expect(mockOnGenerate).toHaveBeenCalledWith("Test prompt", "1:1");
        });
    });

    describe("ImageModelSelector Integration", () => {
        it("should render and handle model selection", async () => {
            const user = userEvent.setup();
            
            render(
                <ImageModelSelector onModelSelect={mockOnModelSelect} />
            );

            // Should show current model
            expect(screen.getByText("Imagen 3.0")).toBeInTheDocument();

            // Click to open selector
            const selector = screen.getByRole("combobox");
            await user.click(selector);

            // Should show model options
            await waitFor(() => {
                expect(screen.getByText("Imagen 3.0 Fast")).toBeInTheDocument();
            });

            // Select different model
            await user.click(screen.getByText("Imagen 3.0 Fast"));

            expect(mockOnModelSelect).toHaveBeenCalled();
        });

        it("should show model capabilities and requirements", async () => {
            const user = userEvent.setup();
            
            render(<ImageModelSelector />);

            const selector = screen.getByRole("combobox");
            await user.click(selector);

            // Should show free model indicator
            await waitFor(() => {
                const freeModel = screen.getByText("Imagen 3.0 Fast");
                expect(freeModel).toBeInTheDocument();
            });
        });
    });

    describe("ImagePreview Integration", () => {
        const mockImageContent: ImageContent = {
            prompt: "A beautiful sunset",
            imageUrl: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            imageData: new Uint8Array([1, 2, 3, 4, 5]),
            model: ModelEnum.GEMINI_IMAGEN_3_0,
            aspectRatio: "1:1",
            generatedAt: new Date(),
            size: 5,
        };

        it("should render image preview with metadata", () => {
            render(
                <ImagePreview
                    imageContent={mockImageContent}
                    isGenerating={false}
                />
            );

            // Should show image
            const image = screen.getByRole("button", { name: /Generated image/ });
            expect(image).toBeInTheDocument();

            // Should show prompt
            expect(screen.getByText("A beautiful sunset")).toBeInTheDocument();

            // Should show model info
            expect(screen.getByText(/Model: GEMINI_IMAGEN_3_0/)).toBeInTheDocument();

            // Should show aspect ratio
            expect(screen.getByText(/Ratio: 1:1/)).toBeInTheDocument();
        });

        it("should handle generating state with progress", () => {
            render(
                <ImagePreview
                    imageContent={mockImageContent}
                    isGenerating={true}
                    generationProgress={45}
                />
            );

            // Should show progress indicator
            expect(screen.getByText("Generating image...")).toBeInTheDocument();
        });

        it("should handle error state with retry", async () => {
            const user = userEvent.setup();
            
            render(
                <ImagePreview
                    imageContent={mockImageContent}
                    error="Generation failed"
                    onRetry={mockOnRetry}
                />
            );

            // Should show error message
            expect(screen.getByText("Generation failed")).toBeInTheDocument();

            // Should show retry button
            const retryButton = screen.getByText("Retry");
            await user.click(retryButton);

            expect(mockOnRetry).toHaveBeenCalled();
        });

        it("should support keyboard navigation for fullscreen", async () => {
            const user = userEvent.setup();
            
            render(
                <ImagePreview
                    imageContent={mockImageContent}
                    isGenerating={false}
                />
            );

            const image = screen.getByRole("button", { name: /Generated image/ });
            
            // Test keyboard activation
            image.focus();
            await user.keyboard("{Enter}");

            // Should open fullscreen (dialog)
            await waitFor(() => {
                expect(screen.getByRole("dialog")).toBeInTheDocument();
            });
        });

        it("should integrate accessibility controls", async () => {
            const user = userEvent.setup();
            
            render(
                <ImagePreview
                    imageContent={mockImageContent}
                    isGenerating={false}
                />
            );

            // Should show accessibility controls
            expect(screen.getByTestId("accessibility-controls")).toBeInTheDocument();

            // Test alt text generation
            const generateAltButton = screen.getByText("Generate Alt Text");
            await user.click(generateAltButton);

            // Should trigger alt text generation
            expect(screen.getByTestId("accessibility-controls")).toBeInTheDocument();
        });
    });

    describe("Complete UI Workflow Integration", () => {
        it("should handle complete image generation workflow", async () => {
            const user = userEvent.setup();
            
            // Render the complete workflow
            const { rerender } = render(
                <div>
                    <ImageModelSelector onModelSelect={mockOnModelSelect} />
                    <ImageGenerationInput
                        onGenerate={mockOnGenerate}
                        isGenerating={false}
                    />
                </div>
            );

            // Step 1: Select model
            const modelSelector = screen.getByRole("combobox");
            await user.click(modelSelector);
            
            await waitFor(() => {
                expect(screen.getByText("Imagen 3.0 Fast")).toBeInTheDocument();
            });
            
            await user.click(screen.getByText("Imagen 3.0 Fast"));

            // Step 2: Enter prompt
            const promptInput = screen.getByPlaceholderText("Describe the image you want to generate...");
            await user.type(promptInput, "A majestic mountain landscape");

            // Step 3: Generate
            const generateButton = screen.getByText("Generate");
            await user.click(generateButton);

            expect(mockOnGenerate).toHaveBeenCalledWith("A majestic mountain landscape", "1:1");

            // Step 4: Show generating state
            rerender(
                <div>
                    <ImageModelSelector onModelSelect={mockOnModelSelect} />
                    <ImageGenerationInput
                        onGenerate={mockOnGenerate}
                        isGenerating={true}
                    />
                    <ImagePreview
                        imageContent={{
                            prompt: "A majestic mountain landscape",
                            model: ModelEnum.GEMINI_IMAGEN_3_0_FAST,
                            aspectRatio: "1:1",
                            generatedAt: new Date(),
                        } as ImageContent}
                        isGenerating={true}
                        generationProgress={30}
                    />
                </div>
            );

            expect(screen.getByText("Generating...")).toBeInTheDocument();
            expect(screen.getByText("Generating image...")).toBeInTheDocument();

            // Step 5: Show completed result
            rerender(
                <div>
                    <ImageModelSelector onModelSelect={mockOnModelSelect} />
                    <ImageGenerationInput
                        onGenerate={mockOnGenerate}
                        isGenerating={false}
                    />
                    <ImagePreview
                        imageContent={{
                            prompt: "A majestic mountain landscape",
                            imageUrl: "data:image/png;base64,test",
                            imageData: new Uint8Array([1, 2, 3]),
                            model: ModelEnum.GEMINI_IMAGEN_3_0_FAST,
                            aspectRatio: "1:1",
                            generatedAt: new Date(),
                            size: 3,
                        }}
                        isGenerating={false}
                    />
                </div>
            );

            expect(screen.getByText("A majestic mountain landscape")).toBeInTheDocument();
            expect(screen.getByRole("button", { name: /Generated image/ })).toBeInTheDocument();
        });

        it("should handle error recovery workflow", async () => {
            const user = userEvent.setup();
            
            // Start with error state
            render(
                <div>
                    <ImageGenerationInput
                        onGenerate={mockOnGenerate}
                        isGenerating={false}
                    />
                    <ImagePreview
                        imageContent={{
                            prompt: "Failed generation",
                            model: ModelEnum.GEMINI_IMAGEN_3_0,
                            aspectRatio: "1:1",
                            generatedAt: new Date(),
                        } as ImageContent}
                        error="Rate limit exceeded. Please try again later."
                        onRetry={mockOnRetry}
                    />
                </div>
            );

            // Should show error
            expect(screen.getByText("Rate limit exceeded. Please try again later.")).toBeInTheDocument();

            // Click retry
            const retryButton = screen.getByText("Retry");
            await user.click(retryButton);

            expect(mockOnRetry).toHaveBeenCalled();

            // Or modify prompt and try again
            const promptInput = screen.getByPlaceholderText("Describe the image you want to generate...");
            await user.clear(promptInput);
            await user.type(promptInput, "A simple landscape");

            const generateButton = screen.getByText("Generate");
            await user.click(generateButton);

            expect(mockOnGenerate).toHaveBeenCalledWith("A simple landscape", "1:1");
        });
    });
});
