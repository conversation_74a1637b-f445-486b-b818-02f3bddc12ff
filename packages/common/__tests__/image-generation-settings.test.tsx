import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it, jest, beforeEach } from "vitest";
import { ImageGenerationSettings } from "../components/image-generation-settings";
import { 
    applyImageGenerationSettings, 
    validateImageGenerationParams,
    getRecommendedSettingsForModel,
    createDefaultImageGenerationSettings,
    mergeSettingsWithRecommendations
} from "../lib/image-generation-utils";
import { ModelEnum } from "@repo/ai/models";

// Mock the app store
jest.mock("../store", () => ({
    useAppStore: jest.fn(() => ({
        imageGenerationSettings: {
            defaultQuality: "standard",
            defaultStyle: "natural",
            enableMultipleImages: false,
            defaultImageCount: 1,
            maxImageCount: 4,
            defaultSize: "1024x1024",
            preferAspectRatio: true,
            defaultAspectRatio: "1:1",
            enableSeed: false,
            enableCustomSeed: false,
            enableBatching: true,
            maxImagesPerCall: 4,
            enableTimeout: true,
            timeoutSeconds: 60,
            openaiSettings: {
                style: "natural",
                quality: "standard",
                responseFormat: "b64_json",
            },
            enableContentFilter: true,
            enableSafetyCheck: true,
            autoSaveGenerated: true,
            compressImages: true,
            includeMetadata: true,
        },
        setImageGenerationSettings: jest.fn(),
    })),
}));

describe("Image Generation Settings", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("ImageGenerationSettings Component", () => {
        it("should render all settings sections", () => {
            render(<ImageGenerationSettings />);

            // Check for main sections
            expect(screen.getByText("Quality & Style")).toBeInTheDocument();
            expect(screen.getByText("Generation Options")).toBeInTheDocument();
            expect(screen.getByText("Size & Format")).toBeInTheDocument();
            expect(screen.getByText("Advanced Options")).toBeInTheDocument();
            expect(screen.getByText("Safety & Content")).toBeInTheDocument();
            expect(screen.getByText("Storage & Export")).toBeInTheDocument();
        });

        it("should show quality options", () => {
            render(<ImageGenerationSettings />);

            expect(screen.getByText("Default Quality")).toBeInTheDocument();
            expect(screen.getByText("Standard (Faster, Lower Cost)")).toBeInTheDocument();
        });

        it("should show style options", () => {
            render(<ImageGenerationSettings />);

            expect(screen.getByText("Default Style")).toBeInTheDocument();
            expect(screen.getByText("Natural (Realistic)")).toBeInTheDocument();
        });

        it("should show OpenAI specific settings", () => {
            render(<ImageGenerationSettings />);

            expect(screen.getByText("OpenAI DALL-E Settings")).toBeInTheDocument();
        });

        it("should handle multiple images toggle", async () => {
            const user = userEvent.setup();
            render(<ImageGenerationSettings />);

            const multipleImagesToggle = screen.getByRole("switch", { name: /Enable Multiple Images/ });
            expect(multipleImagesToggle).toBeInTheDocument();

            // Toggle should be off by default
            expect(multipleImagesToggle).not.toBeChecked();
        });

        it("should show aspect ratio vs size preference", () => {
            render(<ImageGenerationSettings />);

            expect(screen.getByText("Use Aspect Ratio (Recommended)")).toBeInTheDocument();
            expect(screen.getByText("Default Aspect Ratio")).toBeInTheDocument();
        });

        it("should show advanced seed options", () => {
            render(<ImageGenerationSettings />);

            expect(screen.getByText("Enable Seed Control")).toBeInTheDocument();
        });

        it("should show safety settings", () => {
            render(<ImageGenerationSettings />);

            expect(screen.getByText("Content Filter")).toBeInTheDocument();
            expect(screen.getByText("Safety Check")).toBeInTheDocument();
        });

        it("should show storage settings", () => {
            render(<ImageGenerationSettings />);

            expect(screen.getByText("Auto-Save Generated Images")).toBeInTheDocument();
            expect(screen.getByText("Compress Images")).toBeInTheDocument();
            expect(screen.getByText("Include Generation Metadata")).toBeInTheDocument();
        });

        it("should have reset to defaults button", () => {
            render(<ImageGenerationSettings />);

            const resetButton = screen.getByText("Reset to Defaults");
            expect(resetButton).toBeInTheDocument();
        });
    });

    describe("Image Generation Utils", () => {
        describe("applyImageGenerationSettings", () => {
            it("should apply default quality setting", () => {
                const settings = createDefaultImageGenerationSettings();
                settings.defaultQuality = "hd";

                const params = applyImageGenerationSettings(
                    { prompt: "test", model: ModelEnum.OPENAI_DALL_E_3 },
                    settings
                );

                expect(params.quality).toBe("hd");
            });

            it("should apply default style setting", () => {
                const settings = createDefaultImageGenerationSettings();
                settings.defaultStyle = "vivid";

                const params = applyImageGenerationSettings(
                    { prompt: "test", model: ModelEnum.OPENAI_DALL_E_3 },
                    settings
                );

                expect(params.style).toBe("vivid");
            });

            it("should prefer aspect ratio over size when enabled", () => {
                const settings = createDefaultImageGenerationSettings();
                settings.preferAspectRatio = true;
                settings.defaultAspectRatio = "16:9";

                const params = applyImageGenerationSettings(
                    { prompt: "test", model: ModelEnum.OPENAI_DALL_E_3 },
                    settings
                );

                expect(params.aspectRatio).toBe("16:9");
                expect(params.size).toBeUndefined();
            });

            it("should use size when aspect ratio is disabled", () => {
                const settings = createDefaultImageGenerationSettings();
                settings.preferAspectRatio = false;
                settings.defaultSize = "1792x1024";

                const params = applyImageGenerationSettings(
                    { prompt: "test", model: ModelEnum.OPENAI_DALL_E_3 },
                    settings
                );

                expect(params.size).toBe("1792x1024");
            });

            it("should apply seed when enabled", () => {
                const settings = createDefaultImageGenerationSettings();
                settings.enableSeed = true;
                settings.enableCustomSeed = false;
                settings.defaultSeed = 12345;

                const params = applyImageGenerationSettings(
                    { prompt: "test", model: ModelEnum.OPENAI_DALL_E_3 },
                    settings
                );

                expect(params.seed).toBe(12345);
            });

            it("should apply multiple images setting", () => {
                const settings = createDefaultImageGenerationSettings();
                settings.enableMultipleImages = true;
                settings.defaultImageCount = 3;

                const params = applyImageGenerationSettings(
                    { prompt: "test", model: ModelEnum.OPENAI_DALL_E_3 },
                    settings
                );

                expect(params.n).toBe(3);
            });

            it("should apply timeout setting", () => {
                const settings = createDefaultImageGenerationSettings();
                settings.enableTimeout = true;
                settings.timeoutSeconds = 120;

                const params = applyImageGenerationSettings(
                    { prompt: "test", model: ModelEnum.OPENAI_DALL_E_3 },
                    settings
                );

                expect(params.timeoutMs).toBe(120000);
            });

            it("should apply OpenAI provider settings", () => {
                const settings = createDefaultImageGenerationSettings();
                settings.openaiSettings = {
                    style: "vivid",
                    quality: "hd",
                    responseFormat: "url",
                };

                const params = applyImageGenerationSettings(
                    { prompt: "test", model: ModelEnum.OPENAI_DALL_E_3 },
                    settings
                );

                expect(params.providerOptions?.openai).toEqual({
                    style: "vivid",
                    quality: "hd",
                    responseFormat: "url",
                });
            });

            it("should allow user overrides", () => {
                const settings = createDefaultImageGenerationSettings();
                settings.defaultQuality = "standard";

                const params = applyImageGenerationSettings(
                    { prompt: "test", model: ModelEnum.OPENAI_DALL_E_3 },
                    settings,
                    { quality: "hd" } // User override
                );

                expect(params.quality).toBe("hd");
            });
        });

        describe("validateImageGenerationParams", () => {
            it("should validate image count against max", () => {
                const settings = createDefaultImageGenerationSettings();
                settings.maxImageCount = 2;

                const result = validateImageGenerationParams(
                    { prompt: "test", model: ModelEnum.OPENAI_DALL_E_3, n: 5 },
                    settings
                );

                expect(result.valid).toBe(false);
                expect(result.errors).toContain("Number of images (5) exceeds maximum allowed (2)");
            });

            it("should validate batch size", () => {
                const settings = createDefaultImageGenerationSettings();
                settings.enableBatching = true;
                settings.maxImagesPerCall = 2;

                const result = validateImageGenerationParams(
                    { prompt: "test", model: ModelEnum.OPENAI_DALL_E_3, n: 5 },
                    settings
                );

                expect(result.valid).toBe(false);
                expect(result.errors).toContain("Batch size (5) exceeds maximum per call (2)");
            });

            it("should validate seed usage", () => {
                const settings = createDefaultImageGenerationSettings();
                settings.enableSeed = false;

                const result = validateImageGenerationParams(
                    { prompt: "test", model: ModelEnum.OPENAI_DALL_E_3, seed: 12345 },
                    settings
                );

                expect(result.valid).toBe(false);
                expect(result.errors).toContain("Seed usage is disabled in settings");
            });

            it("should validate custom seed usage", () => {
                const settings = createDefaultImageGenerationSettings();
                settings.enableSeed = true;
                settings.enableCustomSeed = false;
                settings.defaultSeed = 12345;

                const result = validateImageGenerationParams(
                    { prompt: "test", model: ModelEnum.OPENAI_DALL_E_3, seed: 67890 },
                    settings
                );

                expect(result.valid).toBe(false);
                expect(result.errors).toContain("Custom seeds are disabled in settings");
            });

            it("should pass validation for valid params", () => {
                const settings = createDefaultImageGenerationSettings();

                const result = validateImageGenerationParams(
                    { prompt: "test", model: ModelEnum.OPENAI_DALL_E_3, n: 1 },
                    settings
                );

                expect(result.valid).toBe(true);
                expect(result.errors).toHaveLength(0);
            });
        });

        describe("getRecommendedSettingsForModel", () => {
            it("should recommend settings for DALL-E 3", () => {
                const recommendations = getRecommendedSettingsForModel(ModelEnum.OPENAI_DALL_E_3);

                expect(recommendations.defaultQuality).toBe("hd");
                expect(recommendations.defaultStyle).toBe("vivid");
                expect(recommendations.enableMultipleImages).toBe(true);
                expect(recommendations.maxImageCount).toBe(4);
            });

            it("should recommend settings for DALL-E 2", () => {
                const recommendations = getRecommendedSettingsForModel(ModelEnum.OPENAI_DALL_E_2);

                expect(recommendations.defaultQuality).toBe("standard");
                expect(recommendations.defaultStyle).toBe("natural");
                expect(recommendations.enableMultipleImages).toBe(false);
                expect(recommendations.maxImageCount).toBe(1);
                expect(recommendations.defaultAspectRatio).toBe("1:1");
            });

            it("should recommend settings for ChatGPT Image-1", () => {
                const recommendations = getRecommendedSettingsForModel(ModelEnum.OPENAI_CHATGPT_IMAGE_1);

                expect(recommendations.defaultQuality).toBe("hd");
                expect(recommendations.defaultStyle).toBe("vivid");
                expect(recommendations.enableMultipleImages).toBe(true);
                expect(recommendations.maxImageCount).toBe(4);
            });

            it("should recommend settings for Gemini models", () => {
                const recommendations = getRecommendedSettingsForModel(ModelEnum.GEMINI_IMAGEN_3_0);

                expect(recommendations.defaultQuality).toBe("standard");
                expect(recommendations.defaultStyle).toBe("natural");
                expect(recommendations.enableMultipleImages).toBe(false);
                expect(recommendations.maxImageCount).toBe(1);
                expect(recommendations.preferAspectRatio).toBe(true);
            });
        });

        describe("mergeSettingsWithRecommendations", () => {
            it("should merge user settings with model recommendations", () => {
                const userSettings = createDefaultImageGenerationSettings();
                userSettings.defaultQuality = "standard";
                userSettings.enableMultipleImages = false;

                const merged = mergeSettingsWithRecommendations(userSettings, ModelEnum.OPENAI_DALL_E_3);

                // Should keep user preference for some settings
                expect(merged.enableMultipleImages).toBe(true); // Overridden by recommendation
                expect(merged.defaultQuality).toBe("hd"); // Overridden by recommendation
                
                // Should keep user settings for non-recommended fields
                expect(merged.compressImages).toBe(userSettings.compressImages);
            });
        });
    });
});
