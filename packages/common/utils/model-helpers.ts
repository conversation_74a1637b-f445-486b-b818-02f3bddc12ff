import { getModelFromChatMode, isImageModel } from "@repo/ai/models";
import type { ChatMode } from "@repo/shared/config";
import { isGeminiModel } from "@repo/shared/utils";

/**
 * Check if a chat mode is for image generation
 * @param chatMode The chat mode to check
 * @returns True if the chat mode is for image generation
 */
export const isImageGenerationMode = (chatMode: ChatMode): boolean => {
    const modelEnum = getModelFromChatMode(chatMode);
    return isImageModel(modelEnum);
};

/**
 * Check if a chat mode is a Gemini model
 * @param chatMode The chat mode to check
 * @returns True if the chat mode is a Gemini model
 * @deprecated Use isGeminiModel from @repo/shared/utils instead
 */
export const isGeminiChatMode = (chatMode: ChatMode): boolean => {
    return isGeminiModel(chatMode);
};
