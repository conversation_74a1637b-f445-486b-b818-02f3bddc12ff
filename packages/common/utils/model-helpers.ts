import { getModelFromChatMode, isImageModel } from "@repo/ai/models";
import { ChatMode } from "@repo/shared/config";

/**
 * Check if a chat mode is for image generation
 * @param chatMode The chat mode to check
 * @returns True if the chat mode is for image generation
 */
export const isImageGenerationMode = (chatMode: ChatMode): boolean => {
    const modelEnum = getModelFromChatMode(chatMode);
    return isImageModel(modelEnum);
};

/**
 * Check if a chat mode is a Gemini model
 * @param chatMode The chat mode to check
 * @returns True if the chat mode is a Gemini model
 */
export const isGeminiModel = (chatMode: ChatMode): boolean => {
    return (
        chatMode === ChatMode.GEMINI_2_5_PRO ||
        chatMode === ChatMode.GEMINI_2_5_FLASH ||
        chatMode === ChatMode.GEMINI_2_5_FLASH_LITE ||
        chatMode === ChatMode.GEMINI_IMAGEN_3_0 ||
        chatMode === ChatMode.GEMINI_IMAGEN_3_0_FAST
    );
};
