import { log } from "@repo/shared/logger";

/**
 * Interface for image generation performance metrics
 */
export interface ImageGenerationMetrics {
    operationId: string;
    model: string;
    promptLength: number;
    aspectRatio: string;
    startTime: number;
    endTime?: number;
    duration?: number;
    imageSize?: number;
    compressionRatio?: number;
    success: boolean;
    errorType?: string;
    retryCount?: number;
    cacheHit?: boolean;
}

/**
 * Interface for progress tracking
 */
export interface ImageGenerationProgress {
    operationId: string;
    stage: 'initializing' | 'generating' | 'processing' | 'compressing' | 'storing' | 'complete' | 'error';
    progress: number; // 0-100
    message: string;
    timestamp: number;
}

/**
 * Performance monitor for image generation operations
 */
export class ImagePerformanceMonitor {
    private static instance: ImagePerformanceMonitor;
    private metrics: Map<string, ImageGenerationMetrics> = new Map();
    private progressCallbacks: Map<string, (progress: ImageGenerationProgress) => void> = new Map();
    private performanceHistory: ImageGenerationMetrics[] = [];
    private maxHistorySize = 100;

    static getInstance(): ImagePerformanceMonitor {
        if (!ImagePerformanceMonitor.instance) {
            ImagePerformanceMonitor.instance = new ImagePerformanceMonitor();
        }
        return ImagePerformanceMonitor.instance;
    }

    /**
     * Start tracking an image generation operation
     */
    startOperation(
        model: string,
        promptLength: number,
        aspectRatio: string,
        progressCallback?: (progress: ImageGenerationProgress) => void
    ): string {
        const operationId = `img_op_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
        
        const metrics: ImageGenerationMetrics = {
            operationId,
            model,
            promptLength,
            aspectRatio,
            startTime: performance.now(),
            success: false,
            retryCount: 0,
        };

        this.metrics.set(operationId, metrics);
        
        if (progressCallback) {
            this.progressCallbacks.set(operationId, progressCallback);
        }

        this.updateProgress(operationId, 'initializing', 0, 'Starting image generation...');

        log.info("Image generation operation started", {
            operationId,
            model,
            promptLength,
            aspectRatio,
        });

        return operationId;
    }

    /**
     * Update progress for an operation
     */
    updateProgress(
        operationId: string,
        stage: ImageGenerationProgress['stage'],
        progress: number,
        message: string
    ): void {
        const callback = this.progressCallbacks.get(operationId);
        if (callback) {
            const progressUpdate: ImageGenerationProgress = {
                operationId,
                stage,
                progress: Math.max(0, Math.min(100, progress)),
                message,
                timestamp: Date.now(),
            };
            
            callback(progressUpdate);
        }

        // Log significant progress milestones
        if (progress === 0 || progress === 100 || progress % 25 === 0) {
            log.info("Image generation progress", {
                operationId,
                stage,
                progress,
                message,
            });
        }
    }

    /**
     * Complete an operation successfully
     */
    completeOperation(
        operationId: string,
        imageSize: number,
        compressionRatio?: number,
        cacheHit?: boolean
    ): void {
        const metrics = this.metrics.get(operationId);
        if (!metrics) {
            log.warn("Attempted to complete unknown operation", { operationId });
            return;
        }

        metrics.endTime = performance.now();
        metrics.duration = metrics.endTime - metrics.startTime;
        metrics.imageSize = imageSize;
        metrics.compressionRatio = compressionRatio;
        metrics.success = true;
        metrics.cacheHit = cacheHit;

        this.updateProgress(operationId, 'complete', 100, 'Image generation completed successfully');
        this.finalizeOperation(operationId);

        log.info("Image generation operation completed", {
            operationId,
            duration: metrics.duration.toFixed(2),
            imageSize,
            compressionRatio,
            cacheHit,
        });
    }

    /**
     * Mark an operation as failed
     */
    failOperation(operationId: string, errorType: string, retryCount: number = 0): void {
        const metrics = this.metrics.get(operationId);
        if (!metrics) {
            log.warn("Attempted to fail unknown operation", { operationId });
            return;
        }

        metrics.endTime = performance.now();
        metrics.duration = metrics.endTime - metrics.startTime;
        metrics.success = false;
        metrics.errorType = errorType;
        metrics.retryCount = retryCount;

        this.updateProgress(operationId, 'error', 0, `Image generation failed: ${errorType}`);
        this.finalizeOperation(operationId);

        log.error("Image generation operation failed", {
            operationId,
            duration: metrics.duration?.toFixed(2),
            errorType,
            retryCount,
        });
    }

    /**
     * Finalize operation and add to history
     */
    private finalizeOperation(operationId: string): void {
        const metrics = this.metrics.get(operationId);
        if (!metrics) return;

        // Add to history
        this.performanceHistory.push({ ...metrics });
        
        // Maintain history size limit
        if (this.performanceHistory.length > this.maxHistorySize) {
            this.performanceHistory = this.performanceHistory.slice(-this.maxHistorySize);
        }

        // Clean up
        this.metrics.delete(operationId);
        this.progressCallbacks.delete(operationId);
    }

    /**
     * Get performance statistics
     */
    getPerformanceStats(): {
        totalOperations: number;
        successRate: number;
        averageDuration: number;
        averageImageSize: number;
        averageCompressionRatio: number;
        cacheHitRate: number;
        errorBreakdown: Record<string, number>;
        modelPerformance: Record<string, { count: number; avgDuration: number; successRate: number }>;
    } {
        const history = this.performanceHistory;
        const totalOperations = history.length;
        
        if (totalOperations === 0) {
            return {
                totalOperations: 0,
                successRate: 0,
                averageDuration: 0,
                averageImageSize: 0,
                averageCompressionRatio: 0,
                cacheHitRate: 0,
                errorBreakdown: {},
                modelPerformance: {},
            };
        }

        const successful = history.filter(m => m.success);
        const successRate = (successful.length / totalOperations) * 100;
        
        const averageDuration = successful.reduce((sum, m) => sum + (m.duration || 0), 0) / successful.length;
        const averageImageSize = successful.reduce((sum, m) => sum + (m.imageSize || 0), 0) / successful.length;
        const averageCompressionRatio = successful
            .filter(m => m.compressionRatio)
            .reduce((sum, m) => sum + (m.compressionRatio || 0), 0) / successful.length;
        
        const cacheHits = history.filter(m => m.cacheHit).length;
        const cacheHitRate = (cacheHits / totalOperations) * 100;

        // Error breakdown
        const errorBreakdown: Record<string, number> = {};
        history.filter(m => !m.success).forEach(m => {
            const errorType = m.errorType || 'unknown';
            errorBreakdown[errorType] = (errorBreakdown[errorType] || 0) + 1;
        });

        // Model performance
        const modelPerformance: Record<string, { count: number; avgDuration: number; successRate: number }> = {};
        const modelGroups = history.reduce((groups, m) => {
            if (!groups[m.model]) groups[m.model] = [];
            groups[m.model].push(m);
            return groups;
        }, {} as Record<string, ImageGenerationMetrics[]>);

        Object.entries(modelGroups).forEach(([model, metrics]) => {
            const modelSuccessful = metrics.filter(m => m.success);
            modelPerformance[model] = {
                count: metrics.length,
                avgDuration: modelSuccessful.reduce((sum, m) => sum + (m.duration || 0), 0) / modelSuccessful.length,
                successRate: (modelSuccessful.length / metrics.length) * 100,
            };
        });

        return {
            totalOperations,
            successRate,
            averageDuration,
            averageImageSize,
            averageCompressionRatio,
            cacheHitRate,
            errorBreakdown,
            modelPerformance,
        };
    }

    /**
     * Get current active operations
     */
    getActiveOperations(): ImageGenerationMetrics[] {
        return Array.from(this.metrics.values());
    }

    /**
     * Clear performance history
     */
    clearHistory(): void {
        this.performanceHistory = [];
        log.info("Image generation performance history cleared");
    }
}

// Export singleton instance
export const imagePerformanceMonitor = ImagePerformanceMonitor.getInstance();

/**
 * Utility function to measure image generation performance
 */
export const measureImageGeneration = async <T>(
    model: string,
    promptLength: number,
    aspectRatio: string,
    operation: (operationId: string) => Promise<T>,
    progressCallback?: (progress: ImageGenerationProgress) => void
): Promise<T> => {
    const operationId = imagePerformanceMonitor.startOperation(
        model,
        promptLength,
        aspectRatio,
        progressCallback
    );

    try {
        const result = await operation(operationId);
        return result;
    } catch (error) {
        const errorType = error instanceof Error ? error.message : 'unknown';
        imagePerformanceMonitor.failOperation(operationId, errorType);
        throw error;
    }
};
