import type { Thread, ThreadItem } from "@repo/shared/types";
import { log } from "@repo/shared/logger";
import { exportImagesForThread, type ImageRecord } from "./image-storage";

/**
 * Interface for conversation export options
 */
export interface ConversationExportOptions {
    includeImages?: boolean;
    includeMetadata?: boolean;
    format?: "json" | "markdown" | "html";
    imageFormat?: "base64" | "blob" | "exclude";
}

/**
 * Interface for exported conversation data
 */
export interface ExportedConversation {
    thread: Thread;
    threadItems: ThreadItem[];
    images?: Array<{
        id: string;
        messageId: string;
        prompt: string;
        model: string;
        imageData?: Uint8Array;
        imageBase64?: string;
        createdAt: Date;
    }>;
    metadata: {
        exportedAt: Date;
        totalMessages: number;
        totalImages: number;
        totalSize: number;
        includesImages: boolean;
    };
}

/**
 * Export a complete conversation including images
 */
export const exportConversation = async (
    thread: Thread,
    threadItems: ThreadItem[],
    options: ConversationExportOptions = {}
): Promise<ExportedConversation> => {
    const {
        includeImages = true,
        includeMetadata = true,
        imageFormat = "base64"
    } = options;

    log.info("Exporting conversation", {
        threadId: thread.id,
        messageCount: threadItems.length,
        includeImages,
        imageFormat
    });

    let images: ExportedConversation["images"] = [];
    let totalSize = 0;

    // Export images if requested
    if (includeImages && imageFormat !== "exclude") {
        try {
            const imageExport = await exportImagesForThread(thread.id);
            totalSize = imageExport.totalSize;

            images = imageExport.images.map(img => {
                const exportedImage: any = {
                    id: img.id,
                    messageId: threadItems.find(item => 
                        item.imageContent?.prompt === img.prompt
                    )?.id || "",
                    prompt: img.prompt,
                    model: img.model,
                    createdAt: img.createdAt
                };

                // Include image data based on format preference
                if (imageFormat === "base64") {
                    exportedImage.imageBase64 = Buffer.from(img.imageData).toString("base64");
                } else if (imageFormat === "blob") {
                    exportedImage.imageData = img.imageData;
                }

                return exportedImage;
            });

            log.info("Images exported successfully", {
                threadId: thread.id,
                imageCount: images.length,
                totalSize
            });
        } catch (error) {
            log.error("Failed to export images", {
                threadId: thread.id,
                error: error instanceof Error ? error.message : String(error)
            });
            // Continue without images rather than failing the entire export
        }
    }

    const exportedConversation: ExportedConversation = {
        thread,
        threadItems,
        ...(includeImages && { images }),
        metadata: {
            exportedAt: new Date(),
            totalMessages: threadItems.length,
            totalImages: images.length,
            totalSize,
            includesImages: includeImages && images.length > 0
        }
    };

    return exportedConversation;
};

/**
 * Export conversation to JSON format
 */
export const exportConversationAsJson = async (
    thread: Thread,
    threadItems: ThreadItem[],
    options: ConversationExportOptions = {}
): Promise<string> => {
    const conversation = await exportConversation(thread, threadItems, options);
    return JSON.stringify(conversation, null, 2);
};

/**
 * Export conversation to Markdown format
 */
export const exportConversationAsMarkdown = async (
    thread: Thread,
    threadItems: ThreadItem[],
    options: ConversationExportOptions = {}
): Promise<string> => {
    const conversation = await exportConversation(thread, threadItems, options);
    
    let markdown = `# ${thread.title || "Conversation"}\n\n`;
    
    if (options.includeMetadata) {
        markdown += `**Exported:** ${conversation.metadata.exportedAt.toISOString()}\n`;
        markdown += `**Messages:** ${conversation.metadata.totalMessages}\n`;
        if (conversation.metadata.includesImages) {
            markdown += `**Images:** ${conversation.metadata.totalImages}\n`;
        }
        markdown += "\n---\n\n";
    }

    for (const item of conversation.threadItems) {
        // User message
        if (item.query) {
            markdown += `## User\n\n${item.query}\n\n`;
        }

        // Assistant response
        if (item.answer?.text) {
            markdown += `## Assistant\n\n${item.answer.text}\n\n`;
        }

        // Image content
        if (item.imageContent && conversation.images) {
            const image = conversation.images.find(img => 
                img.prompt === item.imageContent?.prompt
            );
            if (image) {
                markdown += `### Generated Image\n\n`;
                markdown += `**Prompt:** ${image.prompt}\n`;
                markdown += `**Model:** ${image.model}\n`;
                if (options.imageFormat === "base64" && image.imageBase64) {
                    markdown += `![Generated Image](data:image/png;base64,${image.imageBase64})\n\n`;
                } else {
                    markdown += `*[Image: ${image.prompt}]*\n\n`;
                }
            }
        }

        markdown += "---\n\n";
    }

    return markdown;
};

/**
 * Download conversation as a file
 */
export const downloadConversation = async (
    thread: Thread,
    threadItems: ThreadItem[],
    options: ConversationExportOptions = {}
): Promise<void> => {
    const { format = "json" } = options;
    
    let content: string;
    let mimeType: string;
    let extension: string;

    switch (format) {
        case "markdown":
            content = await exportConversationAsMarkdown(thread, threadItems, options);
            mimeType = "text/markdown";
            extension = "md";
            break;
        case "json":
        default:
            content = await exportConversationAsJson(thread, threadItems, options);
            mimeType = "application/json";
            extension = "json";
            break;
    }

    // Create and download file
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    
    const filename = `conversation-${thread.id}-${Date.now()}.${extension}`;
    link.href = url;
    link.download = filename;
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);

    log.info("Conversation downloaded", {
        threadId: thread.id,
        format,
        filename,
        includesImages: options.includeImages
    });
};
