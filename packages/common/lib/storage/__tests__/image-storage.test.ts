import { ModelEnum } from "@repo/ai/models";
import { describe, expect, it, jest, beforeEach, afterEach } from "vitest";
import {
    storeGeneratedImage,
    getGeneratedImage,
    getImagesForThread,
    deleteImagesForThread,
    clearAllImages,
    getImageStorageStats,
    exportImagesForThread,
    type ImageRecord,
} from "../image-storage";

// Mock Dexie
jest.mock("dexie", () => {
    const mockTable = {
        add: jest.fn(),
        get: jest.fn(),
        where: jest.fn(() => ({
            equals: jest.fn(() => ({
                toArray: jest.fn(),
                delete: jest.fn(),
            })),
            below: jest.fn(() => ({
                delete: jest.fn(),
            })),
            and: jest.fn(() => ({
                delete: jest.fn(),
            })),
        })),
        toArray: jest.fn(),
        clear: jest.fn(),
        hook: jest.fn(),
    };

    const mockDatabase = {
        images: mockTable,
        version: jest.fn(() => ({
            stores: jest.fn(),
        })),
    };

    return {
        default: jest.fn(() => mockDatabase),
        Table: jest.fn(),
    };
});

// Mock logger
jest.mock("@repo/shared/logger", () => ({
    log: {
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
    },
}));

describe("Image Storage", () => {
    const mockImageData = new Uint8Array([1, 2, 3, 4, 5]);
    const mockThreadId = "thread-123";
    const mockMessageId = "message-456";
    const mockPrompt = "A beautiful sunset";

    beforeEach(() => {
        jest.clearAllMocks();
        
        // Mock window for browser environment
        Object.defineProperty(window, 'location', {
            value: { href: 'http://localhost' },
            writable: true,
        });
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe("storeGeneratedImage", () => {
        it("should store image successfully", async () => {
            const Dexie = require("dexie").default;
            const mockDb = new Dexie();
            mockDb.images.add.mockResolvedValue("img_123");

            const result = await storeGeneratedImage({
                threadId: mockThreadId,
                messageId: mockMessageId,
                imageData: mockImageData,
                prompt: mockPrompt,
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                aspectRatio: "1:1",
                mimeType: "image/png",
            });

            expect(result).toBeTruthy();
            expect(mockDb.images.add).toHaveBeenCalledWith(
                expect.objectContaining({
                    threadId: mockThreadId,
                    messageId: mockMessageId,
                    imageData: mockImageData,
                    prompt: mockPrompt,
                    model: ModelEnum.GEMINI_IMAGEN_3_0,
                    aspectRatio: "1:1",
                    mimeType: "image/png",
                    size: mockImageData.length,
                })
            );
        });

        it("should handle storage errors gracefully", async () => {
            const Dexie = require("dexie").default;
            const mockDb = new Dexie();
            mockDb.images.add.mockRejectedValue(new Error("Storage error"));

            const result = await storeGeneratedImage({
                threadId: mockThreadId,
                messageId: mockMessageId,
                imageData: mockImageData,
                prompt: mockPrompt,
                model: ModelEnum.GEMINI_IMAGEN_3_0,
            });

            expect(result).toBeNull();
        });
    });

    describe("getGeneratedImage", () => {
        it("should retrieve image by ID", async () => {
            const Dexie = require("dexie").default;
            const mockDb = new Dexie();
            const mockImageRecord: ImageRecord = {
                id: "img_123",
                threadId: mockThreadId,
                messageId: mockMessageId,
                imageData: mockImageData,
                prompt: mockPrompt,
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                size: mockImageData.length,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            mockDb.images.get.mockResolvedValue(mockImageRecord);

            const result = await getGeneratedImage("img_123");

            expect(result).toEqual(mockImageRecord);
            expect(mockDb.images.get).toHaveBeenCalledWith("img_123");
        });

        it("should return null for non-existent image", async () => {
            const Dexie = require("dexie").default;
            const mockDb = new Dexie();
            mockDb.images.get.mockResolvedValue(undefined);

            const result = await getGeneratedImage("non-existent");

            expect(result).toBeNull();
        });
    });

    describe("getImagesForThread", () => {
        it("should retrieve all images for a thread", async () => {
            const Dexie = require("dexie").default;
            const mockDb = new Dexie();
            const mockImages: ImageRecord[] = [
                {
                    id: "img_1",
                    threadId: mockThreadId,
                    messageId: "msg_1",
                    imageData: mockImageData,
                    prompt: "Image 1",
                    model: ModelEnum.GEMINI_IMAGEN_3_0,
                    size: mockImageData.length,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
                {
                    id: "img_2",
                    threadId: mockThreadId,
                    messageId: "msg_2",
                    imageData: mockImageData,
                    prompt: "Image 2",
                    model: ModelEnum.GEMINI_IMAGEN_3_0_FAST,
                    size: mockImageData.length,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
            ];

            const mockWhere = mockDb.images.where("threadId");
            const mockEquals = mockWhere.equals(mockThreadId);
            mockEquals.toArray.mockResolvedValue(mockImages);

            const result = await getImagesForThread(mockThreadId);

            expect(result).toEqual(mockImages);
            expect(mockWhere.equals).toHaveBeenCalledWith(mockThreadId);
        });
    });

    describe("deleteImagesForThread", () => {
        it("should delete all images for a thread", async () => {
            const Dexie = require("dexie").default;
            const mockDb = new Dexie();
            const mockWhere = mockDb.images.where("threadId");
            const mockEquals = mockWhere.equals(mockThreadId);
            mockEquals.delete.mockResolvedValue(2);

            const result = await deleteImagesForThread(mockThreadId);

            expect(result).toBe(2);
            expect(mockWhere.equals).toHaveBeenCalledWith(mockThreadId);
            expect(mockEquals.delete).toHaveBeenCalled();
        });
    });

    describe("clearAllImages", () => {
        it("should clear all images from storage", async () => {
            const Dexie = require("dexie").default;
            const mockDb = new Dexie();
            mockDb.images.clear.mockResolvedValue(5);

            const result = await clearAllImages();

            expect(result).toBe(5);
            expect(mockDb.images.clear).toHaveBeenCalled();
        });
    });

    describe("getImageStorageStats", () => {
        it("should return storage statistics", async () => {
            const Dexie = require("dexie").default;
            const mockDb = new Dexie();
            const mockImages: ImageRecord[] = [
                {
                    id: "img_1",
                    threadId: mockThreadId,
                    messageId: "msg_1",
                    imageData: mockImageData,
                    prompt: "Image 1",
                    model: ModelEnum.GEMINI_IMAGEN_3_0,
                    size: 100,
                    createdAt: new Date("2024-01-01"),
                    updatedAt: new Date("2024-01-01"),
                },
                {
                    id: "img_2",
                    threadId: mockThreadId,
                    messageId: "msg_2",
                    imageData: mockImageData,
                    prompt: "Image 2",
                    model: ModelEnum.GEMINI_IMAGEN_3_0_FAST,
                    size: 200,
                    createdAt: new Date("2024-01-02"),
                    updatedAt: new Date("2024-01-02"),
                },
            ];
            mockDb.images.toArray.mockResolvedValue(mockImages);

            const result = await getImageStorageStats();

            expect(result).toEqual({
                totalImages: 2,
                totalSize: 300,
                oldestImage: new Date("2024-01-01"),
                newestImage: new Date("2024-01-02"),
                imagesByModel: {
                    [ModelEnum.GEMINI_IMAGEN_3_0]: 1,
                    [ModelEnum.GEMINI_IMAGEN_3_0_FAST]: 1,
                },
            });
        });

        it("should handle empty storage", async () => {
            const Dexie = require("dexie").default;
            const mockDb = new Dexie();
            mockDb.images.toArray.mockResolvedValue([]);

            const result = await getImageStorageStats();

            expect(result).toEqual({
                totalImages: 0,
                totalSize: 0,
                imagesByModel: {},
            });
        });
    });

    describe("exportImagesForThread", () => {
        it("should export images for conversation export", async () => {
            const Dexie = require("dexie").default;
            const mockDb = new Dexie();
            const mockImages: ImageRecord[] = [
                {
                    id: "img_1",
                    threadId: mockThreadId,
                    messageId: "msg_1",
                    imageData: mockImageData,
                    prompt: "Image 1",
                    model: ModelEnum.GEMINI_IMAGEN_3_0,
                    size: 100,
                    createdAt: new Date("2024-01-01"),
                    updatedAt: new Date("2024-01-01"),
                },
            ];

            const mockWhere = mockDb.images.where("threadId");
            const mockEquals = mockWhere.equals(mockThreadId);
            mockEquals.toArray.mockResolvedValue(mockImages);

            const result = await exportImagesForThread(mockThreadId);

            expect(result).toEqual({
                images: [
                    {
                        id: "img_1",
                        prompt: "Image 1",
                        model: ModelEnum.GEMINI_IMAGEN_3_0,
                        imageData: mockImageData,
                        createdAt: new Date("2024-01-01"),
                    },
                ],
                totalSize: 100,
            });
        });
    });
});
