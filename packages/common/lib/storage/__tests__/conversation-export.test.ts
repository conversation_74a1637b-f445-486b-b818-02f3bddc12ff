import { ModelEnum } from "@repo/ai/models";
import { describe, expect, it, jest, beforeEach } from "vitest";
import { exportConversation, exportConversationAsJson, exportConversationAsMarkdown } from "../conversation-export";
import * as imageStorage from "../image-storage";

// Mock the image storage module
jest.mock("../image-storage", () => ({
    exportImagesForThread: jest.fn(),
}));

// Mock logger
jest.mock("@repo/shared/logger", () => ({
    log: {
        info: jest.fn(),
        error: jest.fn(),
    },
}));

describe("Conversation Export", () => {
    const mockThread = {
        id: "thread-123",
        title: "Test Conversation",
        createdAt: new Date("2024-01-01"),
        updatedAt: new Date("2024-01-02"),
    };

    const mockThreadItems = [
        {
            id: "item-1",
            threadId: "thread-123",
            query: "Generate an image of a sunset",
            createdAt: new Date("2024-01-01T10:00:00Z"),
            updatedAt: new Date("2024-01-01T10:00:00Z"),
            mode: "gemini-imagen-3.0",
            messageType: "image",
            imageContent: {
                prompt: "A beautiful sunset over mountains",
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                aspectRatio: "1:1",
            },
        },
        {
            id: "item-2",
            threadId: "thread-123",
            query: "Tell me about AI",
            answer: {
                text: "AI is a field of computer science...",
            },
            createdAt: new Date("2024-01-01T10:05:00Z"),
            updatedAt: new Date("2024-01-01T10:05:00Z"),
            mode: "gemini-2.5-pro",
            messageType: "text",
        },
    ];

    const mockImageData = new Uint8Array([1, 2, 3, 4, 5]);
    const mockExportedImages = {
        images: [
            {
                id: "img-1",
                prompt: "A beautiful sunset over mountains",
                model: ModelEnum.GEMINI_IMAGEN_3_0,
                imageData: mockImageData,
                createdAt: new Date("2024-01-01T10:00:00Z"),
            },
        ],
        totalSize: 5,
    };

    beforeEach(() => {
        jest.clearAllMocks();
        (imageStorage.exportImagesForThread as jest.Mock).mockResolvedValue(mockExportedImages);
    });

    describe("exportConversation", () => {
        it("should export conversation with images included by default", async () => {
            const result = await exportConversation(mockThread, mockThreadItems);

            expect(result.thread).toEqual(mockThread);
            expect(result.threadItems).toEqual(mockThreadItems);
            expect(result.images).toEqual(mockExportedImages.images);
            expect(result.metadata.totalImages).toBe(1);
            expect(result.metadata.totalSize).toBe(5);
            expect(result.metadata.includesImages).toBe(true);
            expect(imageStorage.exportImagesForThread).toHaveBeenCalledWith(mockThread.id);
        });

        it("should export conversation without images when includeImages is false", async () => {
            const result = await exportConversation(mockThread, mockThreadItems, { includeImages: false });

            expect(result.thread).toEqual(mockThread);
            expect(result.threadItems).toEqual(mockThreadItems);
            expect(result.images).toBeUndefined();
            expect(result.metadata.totalImages).toBe(0);
            expect(result.metadata.includesImages).toBe(false);
            expect(imageStorage.exportImagesForThread).not.toHaveBeenCalled();
        });

        it("should convert image data to base64 when imageFormat is base64", async () => {
            const result = await exportConversation(mockThread, mockThreadItems, { imageFormat: "base64" });

            expect(result.images?.[0].imageBase64).toBeDefined();
            expect(result.images?.[0].imageData).toBeUndefined();
        });

        it("should include raw image data when imageFormat is blob", async () => {
            const result = await exportConversation(mockThread, mockThreadItems, { imageFormat: "blob" });

            expect(result.images?.[0].imageData).toEqual(mockImageData);
            expect(result.images?.[0].imageBase64).toBeUndefined();
        });

        it("should handle errors during image export", async () => {
            (imageStorage.exportImagesForThread as jest.Mock).mockRejectedValue(new Error("Export failed"));

            const result = await exportConversation(mockThread, mockThreadItems);

            expect(result.thread).toEqual(mockThread);
            expect(result.threadItems).toEqual(mockThreadItems);
            expect(result.images).toEqual([]);
            expect(result.metadata.totalImages).toBe(0);
            expect(result.metadata.includesImages).toBe(false);
        });
    });

    describe("exportConversationAsJson", () => {
        it("should export conversation as JSON string", async () => {
            const jsonResult = await exportConversationAsJson(mockThread, mockThreadItems);
            
            expect(typeof jsonResult).toBe("string");
            
            const parsed = JSON.parse(jsonResult);
            expect(parsed.thread).toEqual(expect.objectContaining({
                id: mockThread.id,
                title: mockThread.title,
            }));
            expect(parsed.threadItems).toHaveLength(2);
            expect(parsed.images).toHaveLength(1);
        });
    });

    describe("exportConversationAsMarkdown", () => {
        it("should export conversation as Markdown string", async () => {
            const markdownResult = await exportConversationAsMarkdown(mockThread, mockThreadItems);
            
            expect(typeof markdownResult).toBe("string");
            expect(markdownResult).toContain("# Test Conversation");
            expect(markdownResult).toContain("## User");
            expect(markdownResult).toContain("Generate an image of a sunset");
            expect(markdownResult).toContain("## Assistant");
            expect(markdownResult).toContain("AI is a field of computer science");
            expect(markdownResult).toContain("### Generated Image");
            expect(markdownResult).toContain("**Prompt:** A beautiful sunset over mountains");
            expect(markdownResult).toContain("**Model:** imagen-3.0-generate-001");
        });

        it("should include base64 image data in markdown when imageFormat is base64", async () => {
            const markdownResult = await exportConversationAsMarkdown(mockThread, mockThreadItems, {
                imageFormat: "base64",
            });
            
            expect(markdownResult).toContain("![Generated Image](data:image/png;base64,");
        });

        it("should not include image data in markdown when imageFormat is exclude", async () => {
            const markdownResult = await exportConversationAsMarkdown(mockThread, mockThreadItems, {
                imageFormat: "exclude",
            });
            
            expect(markdownResult).not.toContain("![Generated Image]");
            expect(markdownResult).toContain("*[Image: A beautiful sunset over mountains]*");
        });

        it("should include metadata when includeMetadata is true", async () => {
            const markdownResult = await exportConversationAsMarkdown(mockThread, mockThreadItems, {
                includeMetadata: true,
            });
            
            expect(markdownResult).toContain("**Exported:**");
            expect(markdownResult).toContain("**Messages:** 2");
            expect(markdownResult).toContain("**Images:** 1");
        });

        it("should not include metadata when includeMetadata is false", async () => {
            const markdownResult = await exportConversationAsMarkdown(mockThread, mockThreadItems, {
                includeMetadata: false,
            });
            
            expect(markdownResult).not.toContain("**Exported:**");
            expect(markdownResult).not.toContain("**Messages:**");
        });
    });
});
