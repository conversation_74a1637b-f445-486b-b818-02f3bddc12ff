import type { ModelEnum } from "@repo/ai/models";
import { log } from "@repo/shared/logger";
import <PERSON><PERSON>, { type Table } from "dexie";

/**
 * Interface for image records stored in IndexedDB
 */
export interface ImageRecord {
    id: string;
    threadId: string;
    messageId: string;
    imageData: Uint8Array;
    compressedImageData?: Uint8Array;
    prompt: string;
    model: ModelEnum;
    aspectRatio?: string;
    dimensions?: string;
    size: number;
    mimeType?: string;
    createdAt: Date;
    updatedAt: Date;
    // Metadata for privacy and cleanup
    isTemporary?: boolean;
    expiresAt?: Date;
}

/**
 * Interface for image storage statistics
 */
export interface ImageStorageStats {
    totalImages: number;
    totalSize: number;
    oldestImage?: Date;
    newestImage?: Date;
    imagesByModel: Record<string, number>;
}

/**
 * Image database class extending Dexie for IndexedDB operations
 */
class ImageDatabase extends Dexie {
    images!: Table<ImageRecord>;

    constructor(userId?: string) {
        // Create user-specific database name for per-account isolation
        const dbName = userId ? `ImageDatabase_${userId}` : "ImageDatabase_anonymous";

        super(dbName);

        // Define schema version 1
        this.version(1).stores({
            images: "++id, threadId, messageId, model, createdAt, updatedAt, expiresAt",
        });

        // Add hooks for automatic timestamp updates
        this.images.hook("creating", (_primKey, obj, _trans) => {
            obj.createdAt = new Date();
            obj.updatedAt = new Date();
        });

        this.images.hook("updating", (modifications, _primKey, _obj, _trans) => {
            modifications.updatedAt = new Date();
        });
    }
}

// Global database instance management
let db: ImageDatabase | null = null;
let currentUserId: string | null = null;

/**
 * Get the current database instance, ensuring it's initialized
 */
function getDatabase(): ImageDatabase | null {
    if (typeof window === "undefined") {
        return null;
    }

    if (!db) {
        // Auto-initialize if not already done
        initializeUserDatabase(null);
    }

    return db;
}

/**
 * Initialize or switch to a user-specific database
 */
function initializeUserDatabase(userId: string | null) {
    // Only initialize on client side
    if (typeof window === "undefined") {
        log.warn({ context: "ImageDB" }, "Database initialization skipped on server side");
        return null;
    }

    const newUserId = userId || null;

    // Only create new database if user changed
    if (currentUserId !== newUserId) {
        currentUserId = newUserId;
        db = new ImageDatabase(newUserId || undefined);

        log.info(
            { context: "ImageDB", isAnonymous: !newUserId },
            "Initialized image database for user",
        );
    }

    return db;
}

/**
 * Helper function to execute database operations with error handling
 */
async function withDatabaseAsync<T>(
    operation: (database: ImageDatabase) => Promise<T>,
): Promise<T | null> {
    const database = getDatabase();
    if (!database) {
        log.warn({ context: "ImageDB" }, "Database not available for operation");
        return null;
    }

    try {
        return await operation(database);
    } catch (error) {
        log.error(
            { context: "ImageDB", error: error instanceof Error ? error.message : String(error) },
            "Database operation failed",
        );
        return null;
    }
}

/**
 * Compresses image data for efficient storage
 * Uses simple compression by reducing quality for JPEG images
 */
export const compressImageData = (imageData: Uint8Array): Uint8Array => {
    // For now, return original data
    // In a production implementation, you might use a compression library
    // or implement basic compression techniques
    return imageData;
};

/**
 * Generates a unique ID for an image
 */
export const generateImageId = (): string => {
    return `img_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
};

/**
 * Store a generated image in IndexedDB
 */
export const storeGeneratedImage = async (params: {
    threadId: string;
    messageId: string;
    imageData: Uint8Array;
    prompt: string;
    model: ModelEnum;
    aspectRatio?: string;
    dimensions?: string;
    mimeType?: string;
    isTemporary?: boolean;
    expiresAt?: Date;
}): Promise<string | null> => {
    const {
        threadId,
        messageId,
        imageData,
        prompt,
        model,
        aspectRatio,
        dimensions,
        mimeType,
        isTemporary = false,
        expiresAt,
    } = params;

    const imageId = generateImageId();
    const compressedData = compressImageData(imageData);

    const imageRecord: ImageRecord = {
        id: imageId,
        threadId,
        messageId,
        imageData,
        compressedImageData: compressedData,
        prompt,
        model,
        aspectRatio,
        dimensions,
        size: imageData.length,
        mimeType,
        createdAt: new Date(),
        updatedAt: new Date(),
        isTemporary,
        expiresAt,
    };

    const result = await withDatabaseAsync(async (database) => {
        await database.images.add(imageRecord);
        return imageId;
    });

    if (result) {
        log.info(
            {
                context: "ImageDB",
                imageId,
                threadId,
                messageId,
                size: imageData.length,
                model,
            },
            "Image stored successfully",
        );
    }

    return result;
};

/**
 * Retrieve a generated image by ID
 */
export const getGeneratedImage = async (imageId: string): Promise<ImageRecord | null> => {
    return await withDatabaseAsync(async (database) => {
        return await database.images.get(imageId);
    });
};

/**
 * Retrieve all images for a specific thread
 */
export const getImagesForThread = async (threadId: string): Promise<ImageRecord[]> => {
    const result = await withDatabaseAsync(async (database) => {
        return await database.images.where("threadId").equals(threadId).toArray();
    });

    return result || [];
};

/**
 * Retrieve all images for a specific message
 */
export const getImagesForMessage = async (messageId: string): Promise<ImageRecord[]> => {
    const result = await withDatabaseAsync(async (database) => {
        return await database.images.where("messageId").equals(messageId).toArray();
    });

    return result || [];
};

/**
 * Delete a generated image by ID
 */
export const deleteGeneratedImage = async (imageId: string): Promise<boolean> => {
    const result = await withDatabaseAsync(async (database) => {
        const deleteCount = await database.images.delete(imageId);
        return deleteCount > 0;
    });

    if (result) {
        log.info({ context: "ImageDB", imageId }, "Image deleted successfully");
    }

    return result || false;
};

/**
 * Delete all images for a specific thread
 */
export const deleteImagesForThread = async (threadId: string): Promise<number> => {
    const result = await withDatabaseAsync(async (database) => {
        return await database.images.where("threadId").equals(threadId).delete();
    });

    if (result && result > 0) {
        log.info(
            { context: "ImageDB", threadId, count: result },
            "Thread images deleted successfully",
        );
    }

    return result || 0;
};

/**
 * Delete all images for a specific message
 */
export const deleteImagesForMessage = async (messageId: string): Promise<number> => {
    const result = await withDatabaseAsync(async (database) => {
        return await database.images.where("messageId").equals(messageId).delete();
    });

    if (result && result > 0) {
        log.info(
            { context: "ImageDB", messageId, count: result },
            "Message images deleted successfully",
        );
    }

    return result || 0;
};

/**
 * Get storage statistics for images
 */
export const getImageStorageStats = async (): Promise<ImageStorageStats | null> => {
    return await withDatabaseAsync(async (database) => {
        const images = await database.images.toArray();

        if (images.length === 0) {
            return {
                totalImages: 0,
                totalSize: 0,
                imagesByModel: {},
            };
        }

        const totalSize = images.reduce((sum, img) => sum + img.size, 0);
        const imagesByModel: Record<string, number> = {};

        images.forEach((img) => {
            imagesByModel[img.model] = (imagesByModel[img.model] || 0) + 1;
        });

        const dates = images.map((img) => img.createdAt).sort();

        return {
            totalImages: images.length,
            totalSize,
            oldestImage: dates[0],
            newestImage: dates[dates.length - 1],
            imagesByModel,
        };
    });
};

/**
 * Clean up expired images
 */
export const cleanupExpiredImages = async (): Promise<number> => {
    const result = await withDatabaseAsync(async (database) => {
        const now = new Date();
        return await database.images.where("expiresAt").below(now).delete();
    });

    if (result && result > 0) {
        log.info({ context: "ImageDB", count: result }, "Expired images cleaned up");
    }

    return result || 0;
};

/**
 * Clean up temporary images older than specified duration
 */
export const cleanupTemporaryImages = async (olderThanHours: number = 24): Promise<number> => {
    const result = await withDatabaseAsync(async (database) => {
        const cutoffDate = new Date();
        cutoffDate.setHours(cutoffDate.getHours() - olderThanHours);

        return await database.images
            .where("isTemporary")
            .equals(1)
            .and((img) => img.createdAt < cutoffDate)
            .delete();
    });

    if (result && result > 0) {
        log.info(
            { context: "ImageDB", count: result, olderThanHours },
            "Temporary images cleaned up",
        );
    }

    return result || 0;
};

/**
 * Clear all images (for privacy/reset purposes)
 */
export const clearAllImages = async (): Promise<number> => {
    const result = await withDatabaseAsync(async (database) => {
        return await database.images.clear();
    });

    if (result && result > 0) {
        log.info({ context: "ImageDB", count: result }, "All images cleared");
    }

    return result || 0;
};

/**
 * Switch to a different user's image database
 */
export const switchUserImageDatabase = async (userId: string | null): Promise<void> => {
    initializeUserDatabase(userId);
    log.info(
        { context: "ImageDB", userId: userId || "anonymous" },
        "Switched to user image database",
    );
};

/**
 * Export images for a thread (for conversation export)
 */
export const exportImagesForThread = async (
    threadId: string,
): Promise<{
    images: Array<{
        id: string;
        prompt: string;
        model: string;
        imageData: Uint8Array;
        createdAt: Date;
    }>;
    totalSize: number;
}> => {
    const images = await getImagesForThread(threadId);

    const exportData = images.map((img) => ({
        id: img.id,
        prompt: img.prompt,
        model: img.model,
        imageData: img.imageData,
        createdAt: img.createdAt,
    }));

    const totalSize = images.reduce((sum, img) => sum + img.size, 0);

    return {
        images: exportData,
        totalSize,
    };
};

/**
 * Initialize the image database on client side
 */
if (typeof window !== "undefined") {
    // Initialize with anonymous database by default
    initializeUserDatabase(null);
}
