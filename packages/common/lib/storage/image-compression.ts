import { log } from "@repo/shared/logger";

/**
 * Image compression utilities for efficient storage
 */

/**
 * Detects image format from binary data
 */
export const detectImageFormat = (imageData: Uint8Array): string => {
    if (imageData.length < 4) return "unknown";

    // JPEG signature
    if (imageData[0] === 0xff && imageData[1] === 0xd8) {
        return "jpeg";
    }

    // PNG signature
    if (
        imageData[0] === 0x89 &&
        imageData[1] === 0x50 &&
        imageData[2] === 0x4e &&
        imageData[3] === 0x47
    ) {
        return "png";
    }

    // WebP signature
    if (
        imageData[0] === 0x52 &&
        imageData[1] === 0x49 &&
        imageData[2] === 0x46 &&
        imageData[3] === 0x46
    ) {
        return "webp";
    }

    // GIF signature
    if (imageData[0] === 0x47 && imageData[1] === 0x49 && imageData[2] === 0x46) {
        return "gif";
    }

    return "unknown";
};

/**
 * Estimates compression ratio based on image format and size
 */
export const estimateCompressionRatio = (imageData: Uint8Array, format: string): number => {
    const size = imageData.length;

    // Compression ratios based on typical image characteristics
    switch (format) {
        case "png":
            // PNG is already compressed, but we can estimate further compression
            return size > 1024 * 1024 ? 0.7 : 0.85; // Larger images compress better
        case "jpeg":
            // JPEG is already compressed, minimal additional compression
            return 0.95;
        case "webp":
            // WebP is already well compressed
            return 0.9;
        default:
            return 0.8;
    }
};

/**
 * Simple compression using browser's Canvas API (client-side only)
 * This reduces image quality to achieve smaller file sizes
 */
export const compressImageWithCanvas = async (
    imageData: Uint8Array,
    quality: number = 0.8,
    maxWidth: number = 1920,
    maxHeight: number = 1080,
): Promise<Uint8Array> => {
    // Only available in browser environment
    if (typeof window === "undefined" || typeof document === "undefined") {
        log.warn(
            { context: "ImageCompression" },
            "Canvas compression not available on server side",
        );
        return imageData;
    }

    try {
        // Create blob from image data
        const blob = new Blob([imageData]);
        const imageUrl = URL.createObjectURL(blob);

        // Create image element
        const img = new Image();

        return new Promise((resolve, reject) => {
            img.onload = () => {
                try {
                    // Create canvas
                    const canvas = document.createElement("canvas");
                    const ctx = canvas.getContext("2d");

                    if (!ctx) {
                        throw new Error("Could not get canvas context");
                    }

                    // Calculate new dimensions maintaining aspect ratio
                    let { width, height } = img;

                    if (width > maxWidth || height > maxHeight) {
                        const ratio = Math.min(maxWidth / width, maxHeight / height);
                        width *= ratio;
                        height *= ratio;
                    }

                    canvas.width = width;
                    canvas.height = height;

                    // Draw and compress
                    ctx.drawImage(img, 0, 0, width, height);

                    canvas.toBlob(
                        (compressedBlob) => {
                            if (!compressedBlob) {
                                reject(new Error("Failed to compress image"));
                                return;
                            }

                            // Convert blob back to Uint8Array
                            const reader = new FileReader();
                            reader.onload = () => {
                                const arrayBuffer = reader.result as ArrayBuffer;
                                const compressedData = new Uint8Array(arrayBuffer);

                                log.info(
                                    {
                                        context: "ImageCompression",
                                        originalSize: imageData.length,
                                        compressedSize: compressedData.length,
                                        compressionRatio: (
                                            compressedData.length / imageData.length
                                        ).toFixed(2),
                                    },
                                    "Image compressed successfully",
                                );

                                resolve(compressedData);
                            };
                            reader.onerror = () =>
                                reject(new Error("Failed to read compressed blob"));
                            reader.readAsArrayBuffer(compressedBlob);
                        },
                        "image/jpeg",
                        quality,
                    );
                } catch (error) {
                    reject(error);
                } finally {
                    URL.revokeObjectURL(imageUrl);
                }
            };

            img.onerror = () => {
                URL.revokeObjectURL(imageUrl);
                reject(new Error("Failed to load image for compression"));
            };

            img.src = imageUrl;
        });
    } catch (error) {
        log.error(
            {
                context: "ImageCompression",
                error: error instanceof Error ? error.message : String(error),
            },
            "Image compression failed",
        );

        // Return original data if compression fails
        return imageData;
    }
};

/**
 * Lossless compression using simple techniques
 * This is a placeholder for more sophisticated compression algorithms
 */
export const compressImageLossless = (imageData: Uint8Array): Uint8Array => {
    // For now, return original data
    // In a production implementation, you might use:
    // - Run-length encoding for simple patterns
    // - Dictionary-based compression
    // - Huffman coding
    // - Or integrate with libraries like pako for gzip compression

    return imageData;
};

/**
 * Adaptive compression that chooses the best method based on image characteristics
 */
export const compressImageAdaptive = async (
    imageData: Uint8Array,
    targetSizeKB?: number,
    progressCallback?: (progress: number, stage: string) => void,
): Promise<{
    compressedData: Uint8Array;
    compressionRatio: number;
    method: string;
    originalSize: number;
    compressedSize: number;
}> => {
    const startTime = performance.now();
    progressCallback?.(0, "Analyzing image format");

    const format = detectImageFormat(imageData);
    const originalSize = imageData.length;

    log.info("Starting adaptive image compression", {
        originalSize,
        targetSizeKB,
        format,
    });

    // If target size is specified and image is already smaller, return as-is
    if (targetSizeKB && originalSize <= targetSizeKB * 1024) {
        progressCallback?.(100, "Image already optimal size");
        return {
            compressedData: imageData,
            compressionRatio: 1.0,
            method: "none",
            originalSize,
            compressedSize: originalSize,
        };
    }

    let compressedData = imageData;
    let method = "none";

    progressCallback?.(20, "Determining compression strategy");

    // Try canvas compression for larger images
    if (originalSize > 100 * 1024 && typeof window !== "undefined") {
        // > 100KB
        try {
            progressCallback?.(40, "Applying canvas compression");

            // Adjust quality based on target size
            let quality = 0.8;
            if (targetSizeKB) {
                const targetRatio = (targetSizeKB * 1024) / originalSize;
                quality = Math.max(0.3, Math.min(0.9, targetRatio * 1.2));
            }

            compressedData = await compressImageWithCanvas(imageData, quality);
            method = "canvas";

            progressCallback?.(80, "Canvas compression completed");
        } catch (error) {
            log.warn(
                {
                    context: "ImageCompression",
                    error: error instanceof Error ? error.message : String(error),
                },
                "Canvas compression failed, using original data",
            );
            progressCallback?.(60, "Canvas compression failed, trying alternatives");
        }
    }

    // If still too large and target size specified, try more aggressive compression
    if (
        targetSizeKB &&
        compressedData.length > targetSizeKB * 1024 &&
        typeof window !== "undefined"
    ) {
        try {
            compressedData = await compressImageWithCanvas(
                imageData,
                0.5, // Lower quality
                1280, // Smaller max dimensions
                720,
            );
            method = "canvas-aggressive";
        } catch (error) {
            log.warn(
                {
                    context: "ImageCompression",
                    error: error instanceof Error ? error.message : String(error),
                },
                "Aggressive compression failed",
            );
        }
    }

    const compressionRatio = compressedData.length / originalSize;

    log.info(
        {
            context: "ImageCompression",
            originalSize,
            compressedSize: compressedData.length,
            compressionRatio: compressionRatio.toFixed(2),
            method,
            format,
        },
        "Adaptive compression completed",
    );

    return {
        compressedData,
        compressionRatio,
        method,
        originalSize,
        compressedSize: compressedData.length,
    };
};

/**
 * Decompresses image data (placeholder for future implementation)
 */
export const decompressImageData = (compressedData: Uint8Array): Uint8Array => {
    // For now, assume data is not compressed or is in a standard format
    // that browsers can handle directly
    return compressedData;
};

/**
 * Validates image data integrity
 */
export const validateImageData = (
    imageData: Uint8Array,
): {
    valid: boolean;
    format: string;
    estimatedSize: number;
    issues: string[];
} => {
    const issues: string[] = [];
    const format = detectImageFormat(imageData);

    if (format === "unknown") {
        issues.push("Unknown or unsupported image format");
    }

    if (imageData.length === 0) {
        issues.push("Empty image data");
    }

    if (imageData.length > 50 * 1024 * 1024) {
        // 50MB
        issues.push("Image data is very large (>50MB)");
    }

    // Basic header validation for known formats
    if (format === "jpeg" && imageData.length > 2) {
        // JPEG should end with FFD9
        const lastTwo = imageData.slice(-2);
        if (!(lastTwo[0] === 0xff && lastTwo[1] === 0xd9)) {
            issues.push("JPEG data may be corrupted (missing end marker)");
        }
    }

    return {
        valid: issues.length === 0,
        format,
        estimatedSize: imageData.length,
        issues,
    };
};
