import { log } from "@repo/shared/logger";
import { 
    clearAllImages, 
    deleteImagesForThread, 
    cleanupExpiredImages, 
    cleanupTemporaryImages,
    switchUserImageDatabase 
} from "./image-storage";

/**
 * Interface for cleanup options
 */
export interface ConversationCleanupOptions {
    includeImages?: boolean;
    olderThanDays?: number;
    temporaryOnly?: boolean;
}

/**
 * Clear all conversation history including images
 */
export const clearAllConversationHistory = async (
    clearThreads: () => Promise<number>,
    clearThreadItems: () => Promise<number>,
    options: ConversationCleanupOptions = {}
): Promise<{
    threadsCleared: number;
    threadItemsCleared: number;
    imagesCleared: number;
}> => {
    const { includeImages = true } = options;

    log.info("Starting complete conversation history cleanup", {
        includeImages
    });

    let imagesCleared = 0;

    try {
        // Clear images first if requested
        if (includeImages) {
            imagesCleared = await clearAllImages();
        }

        // Clear thread items
        const threadItemsCleared = await clearThreadItems();

        // Clear threads
        const threadsCleared = await clearThreads();

        log.info("Conversation history cleanup completed", {
            threadsCleared,
            threadItemsCleared,
            imagesCleared
        });

        return {
            threadsCleared,
            threadItemsCleared,
            imagesCleared
        };
    } catch (error) {
        log.error("Failed to clear conversation history", {
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
};

/**
 * Clear conversation history for a specific thread including images
 */
export const clearThreadHistory = async (
    threadId: string,
    deleteThread: (threadId: string) => Promise<boolean>,
    deleteThreadItems: (threadId: string) => Promise<number>,
    options: ConversationCleanupOptions = {}
): Promise<{
    threadDeleted: boolean;
    threadItemsDeleted: number;
    imagesDeleted: number;
}> => {
    const { includeImages = true } = options;

    log.info("Clearing thread history", {
        threadId,
        includeImages
    });

    let imagesDeleted = 0;

    try {
        // Delete images for the thread if requested
        if (includeImages) {
            imagesDeleted = await deleteImagesForThread(threadId);
        }

        // Delete thread items
        const threadItemsDeleted = await deleteThreadItems(threadId);

        // Delete the thread
        const threadDeleted = await deleteThread(threadId);

        log.info("Thread history cleared successfully", {
            threadId,
            threadDeleted,
            threadItemsDeleted,
            imagesDeleted
        });

        return {
            threadDeleted,
            threadItemsDeleted,
            imagesDeleted
        };
    } catch (error) {
        log.error("Failed to clear thread history", {
            threadId,
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
};

/**
 * Perform routine cleanup of expired and temporary data
 */
export const performRoutineCleanup = async (
    options: ConversationCleanupOptions = {}
): Promise<{
    expiredImagesCleared: number;
    temporaryImagesCleared: number;
}> => {
    const { olderThanDays = 7 } = options;
    const olderThanHours = olderThanDays * 24;

    log.info("Starting routine cleanup", {
        olderThanDays,
        olderThanHours
    });

    try {
        // Clean up expired images
        const expiredImagesCleared = await cleanupExpiredImages();

        // Clean up temporary images older than specified duration
        const temporaryImagesCleared = await cleanupTemporaryImages(olderThanHours);

        log.info("Routine cleanup completed", {
            expiredImagesCleared,
            temporaryImagesCleared
        });

        return {
            expiredImagesCleared,
            temporaryImagesCleared
        };
    } catch (error) {
        log.error("Routine cleanup failed", {
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
};

/**
 * Switch user context for both conversation and image data
 */
export const switchUserConversationContext = async (
    userId: string | null,
    switchThreadDatabase: (userId: string | null) => Promise<void>
): Promise<void> => {
    log.info("Switching user conversation context", {
        userId: userId || "anonymous"
    });

    try {
        // Switch thread database
        await switchThreadDatabase(userId);

        // Switch image database
        await switchUserImageDatabase(userId);

        log.info("User conversation context switched successfully", {
            userId: userId || "anonymous"
        });
    } catch (error) {
        log.error("Failed to switch user conversation context", {
            userId: userId || "anonymous",
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
};

/**
 * Initialize cleanup routines (to be called on app startup)
 */
export const initializeConversationCleanup = (): void => {
    if (typeof window === "undefined") return;

    // Run initial cleanup
    performRoutineCleanup().catch(error => {
        log.error("Initial cleanup failed", { error });
    });

    // Set up periodic cleanup (every 6 hours)
    setInterval(() => {
        performRoutineCleanup().catch(error => {
            log.error("Periodic cleanup failed", { error });
        });
    }, 6 * 60 * 60 * 1000); // 6 hours

    log.info("Conversation cleanup routines initialized");
};
