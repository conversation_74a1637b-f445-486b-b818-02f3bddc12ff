import type { ModelEnum } from "@repo/ai/models";
import type { ImageGenerationParams } from "@repo/ai/services/image-generation";

/**
 * Image generation settings interface (matching the store)
 */
export interface ImageGenerationSettings {
    defaultQuality: "standard" | "hd";
    defaultStyle: "natural" | "vivid";
    enableMultipleImages: boolean;
    defaultImageCount: number;
    maxImageCount: number;
    defaultSize: "1024x1024" | "1792x1024" | "1024x1792";
    preferAspectRatio: boolean;
    defaultAspectRatio: "1:1" | "16:9" | "9:16" | "4:3" | "3:4";
    enableSeed: boolean;
    defaultSeed?: number;
    enableCustomSeed: boolean;
    enableBatching: boolean;
    maxImagesPerCall: number;
    enableTimeout: boolean;
    timeoutSeconds: number;
    openaiSettings: {
        style: "natural" | "vivid";
        quality: "standard" | "hd";
        responseFormat: "url" | "b64_json";
    };
    enableContentFilter: boolean;
    enableSafetyCheck: boolean;
    autoSaveGenerated: boolean;
    compressImages: boolean;
    includeMetadata: boolean;
}

/**
 * Apply image generation settings to generation parameters
 */
export const applyImageGenerationSettings = (
    baseParams: Partial<ImageGenerationParams>,
    settings: ImageGenerationSettings,
    userOverrides?: Partial<ImageGenerationParams>
): ImageGenerationParams => {
    // Start with base parameters
    const params: ImageGenerationParams = {
        prompt: "",
        model: baseParams.model!,
        ...baseParams,
    };

    // Apply settings defaults
    if (!params.quality && settings.defaultQuality) {
        params.quality = settings.defaultQuality;
    }

    if (!params.style && settings.defaultStyle) {
        params.style = settings.defaultStyle;
    }

    // Apply size/aspect ratio preferences
    if (settings.preferAspectRatio) {
        if (!params.aspectRatio && settings.defaultAspectRatio) {
            params.aspectRatio = settings.defaultAspectRatio;
        }
    } else {
        if (!params.size && settings.defaultSize) {
            params.size = settings.defaultSize;
        }
    }

    // Apply seed settings
    if (settings.enableSeed && !settings.enableCustomSeed && settings.defaultSeed) {
        params.seed = settings.defaultSeed;
    }

    // Apply multiple images setting
    if (settings.enableMultipleImages && !params.n) {
        params.n = Math.min(settings.defaultImageCount, settings.maxImageCount);
    }

    // Apply timeout settings
    if (settings.enableTimeout) {
        params.timeoutMs = settings.timeoutSeconds * 1000;
    }

    // Apply compression settings
    if (settings.compressImages) {
        params.enableCompression = true;
    }

    // Apply provider-specific settings
    if (isOpenAIModel(params.model)) {
        params.providerOptions = {
            openai: {
                style: settings.openaiSettings.style,
                quality: settings.openaiSettings.quality,
                responseFormat: settings.openaiSettings.responseFormat,
            },
        };
    }

    // Apply user overrides last
    if (userOverrides) {
        Object.assign(params, userOverrides);
    }

    return params;
};

/**
 * Check if a model is an OpenAI model
 */
const isOpenAIModel = (model: ModelEnum): boolean => {
    return model.toString().includes("OPENAI");
};

/**
 * Validate image generation parameters against settings
 */
export const validateImageGenerationParams = (
    params: ImageGenerationParams,
    settings: ImageGenerationSettings
): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // Validate image count
    if (params.n && params.n > settings.maxImageCount) {
        errors.push(`Number of images (${params.n}) exceeds maximum allowed (${settings.maxImageCount})`);
    }

    // Validate batch size
    if (settings.enableBatching && params.n && params.n > settings.maxImagesPerCall) {
        errors.push(`Batch size (${params.n}) exceeds maximum per call (${settings.maxImagesPerCall})`);
    }

    // Validate seed usage
    if (params.seed !== undefined && !settings.enableSeed) {
        errors.push("Seed usage is disabled in settings");
    }

    if (params.seed !== undefined && !settings.enableCustomSeed && params.seed !== settings.defaultSeed) {
        errors.push("Custom seeds are disabled in settings");
    }

    return {
        valid: errors.length === 0,
        errors,
    };
};

/**
 * Get recommended settings for a specific model
 */
export const getRecommendedSettingsForModel = (model: ModelEnum): Partial<ImageGenerationSettings> => {
    const recommendations: Partial<ImageGenerationSettings> = {};

    if (isOpenAIModel(model)) {
        // OpenAI-specific recommendations
        if (model.toString().includes("DALL_E_3") || model.toString().includes("CHATGPT_IMAGE_1")) {
            recommendations.defaultQuality = "hd";
            recommendations.defaultStyle = "vivid";
            recommendations.enableMultipleImages = true;
            recommendations.maxImageCount = 4;
        } else if (model.toString().includes("DALL_E_2")) {
            recommendations.defaultQuality = "standard";
            recommendations.defaultStyle = "natural";
            recommendations.enableMultipleImages = false;
            recommendations.maxImageCount = 1;
            recommendations.defaultAspectRatio = "1:1"; // DALL-E 2 only supports square
        }
    } else if (model.toString().includes("GEMINI")) {
        // Gemini-specific recommendations
        recommendations.defaultQuality = "standard";
        recommendations.defaultStyle = "natural";
        recommendations.enableMultipleImages = false;
        recommendations.maxImageCount = 1;
        recommendations.preferAspectRatio = true;
    }

    return recommendations;
};

/**
 * Create default image generation settings
 */
export const createDefaultImageGenerationSettings = (): ImageGenerationSettings => {
    return {
        defaultQuality: "standard",
        defaultStyle: "natural",
        enableMultipleImages: false,
        defaultImageCount: 1,
        maxImageCount: 4,
        defaultSize: "1024x1024",
        preferAspectRatio: true,
        defaultAspectRatio: "1:1",
        enableSeed: false,
        enableCustomSeed: false,
        enableBatching: true,
        maxImagesPerCall: 4,
        enableTimeout: true,
        timeoutSeconds: 60,
        openaiSettings: {
            style: "natural",
            quality: "standard",
            responseFormat: "b64_json",
        },
        enableContentFilter: true,
        enableSafetyCheck: true,
        autoSaveGenerated: true,
        compressImages: true,
        includeMetadata: true,
    };
};

/**
 * Merge user settings with model-specific recommendations
 */
export const mergeSettingsWithRecommendations = (
    userSettings: ImageGenerationSettings,
    model: ModelEnum
): ImageGenerationSettings => {
    const recommendations = getRecommendedSettingsForModel(model);
    return {
        ...userSettings,
        ...recommendations,
    };
};
