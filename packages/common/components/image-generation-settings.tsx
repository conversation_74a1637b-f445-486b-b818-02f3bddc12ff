import {
    Alert,
    AlertDescription,
    <PERSON><PERSON>,
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
    Input,
    Label,
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    Slider,
    Switch,
    TypographyMuted,
} from "@repo/ui";
import { Hash, Image, Info, Layers, Palette, Refresh<PERSON>w, Settings, Shield } from "lucide-react";
import { useState } from "react";

/**
 * Interface for image generation settings
 */
export interface ImageGenerationSettings {
    // Quality and Style Settings
    defaultQuality: "standard" | "hd";
    defaultStyle: "natural" | "vivid";

    // Generation Settings
    enableMultipleImages: boolean;
    defaultImageCount: number;
    maxImageCount: number;

    // Size and Format Settings
    defaultSize: "1024x1024" | "1792x1024" | "1024x1792";
    preferAspectRatio: boolean;
    defaultAspectRatio: "1:1" | "16:9" | "9:16" | "4:3" | "3:4";

    // Advanced Settings
    enableSeed: boolean;
    defaultSeed?: number;
    enableCustomSeed: boolean;

    // Performance Settings
    enableBatching: boolean;
    maxImagesPerCall: number;
    enableTimeout: boolean;
    timeoutSeconds: number;

    // Provider-specific Settings
    openaiSettings: {
        style: "natural" | "vivid";
        quality: "standard" | "hd";
        responseFormat: "url" | "b64_json";
    };

    // Safety and Content Settings
    enableContentFilter: boolean;
    enableSafetyCheck: boolean;

    // Storage and Export Settings
    autoSaveGenerated: boolean;
    compressImages: boolean;
    includeMetadata: boolean;
}

/**
 * Default image generation settings
 */
const DEFAULT_IMAGE_SETTINGS: ImageGenerationSettings = {
    defaultQuality: "standard",
    defaultStyle: "natural",
    enableMultipleImages: false,
    defaultImageCount: 1,
    maxImageCount: 4,
    defaultSize: "1024x1024",
    preferAspectRatio: true,
    defaultAspectRatio: "1:1",
    enableSeed: false,
    enableCustomSeed: false,
    enableBatching: true,
    maxImagesPerCall: 4,
    enableTimeout: true,
    timeoutSeconds: 60,
    openaiSettings: {
        style: "natural",
        quality: "standard",
        responseFormat: "b64_json",
    },
    enableContentFilter: true,
    enableSafetyCheck: true,
    autoSaveGenerated: true,
    compressImages: true,
    includeMetadata: true,
};

/**
 * Image generation settings component
 */
export const ImageGenerationSettings = () => {
    // Get settings from app store (we'll need to add this to the store)
    const [settings, setSettings] = useState<ImageGenerationSettings>(DEFAULT_IMAGE_SETTINGS);

    const updateSetting = <K extends keyof ImageGenerationSettings>(
        key: K,
        value: ImageGenerationSettings[K],
    ) => {
        setSettings((prev) => ({ ...prev, [key]: value }));
    };

    const updateOpenAISetting = <K extends keyof ImageGenerationSettings["openaiSettings"]>(
        key: K,
        value: ImageGenerationSettings["openaiSettings"][K],
    ) => {
        setSettings((prev) => ({
            ...prev,
            openaiSettings: { ...prev.openaiSettings, [key]: value },
        }));
    };

    const resetToDefaults = () => {
        setSettings(DEFAULT_IMAGE_SETTINGS);
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h3 className="text-lg font-semibold">Image Generation Settings</h3>
                    <TypographyMuted>
                        Configure advanced options for AI image generation
                    </TypographyMuted>
                </div>
                <Button variant="outline" size="sm" onClick={resetToDefaults}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Reset to Defaults
                </Button>
            </div>

            {/* Quality and Style Settings */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Palette className="h-5 w-5" />
                        Quality & Style
                    </CardTitle>
                    <CardDescription>
                        Control the quality and artistic style of generated images
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Default Quality */}
                    <div className="space-y-2">
                        <Label>Default Quality</Label>
                        <Select
                            value={settings.defaultQuality}
                            onValueChange={(value: "standard" | "hd") =>
                                updateSetting("defaultQuality", value)
                            }
                        >
                            <SelectTrigger>
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="standard">
                                    Standard (Faster, Lower Cost)
                                </SelectItem>
                                <SelectItem value="hd">HD (Higher Quality, Slower)</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Default Style */}
                    <div className="space-y-2">
                        <Label>Default Style</Label>
                        <Select
                            value={settings.defaultStyle}
                            onValueChange={(value: "natural" | "vivid") =>
                                updateSetting("defaultStyle", value)
                            }
                        >
                            <SelectTrigger>
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="natural">Natural (Realistic)</SelectItem>
                                <SelectItem value="vivid">Vivid (Artistic, Enhanced)</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {/* OpenAI Specific Settings */}
                    <Separator />
                    <div className="space-y-4">
                        <Label className="text-sm font-medium">OpenAI DALL-E Settings</Label>

                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label className="text-xs">Style</Label>
                                <Select
                                    value={settings.openaiSettings.style}
                                    onValueChange={(value: "natural" | "vivid") =>
                                        updateOpenAISetting("style", value)
                                    }
                                >
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="natural">Natural</SelectItem>
                                        <SelectItem value="vivid">Vivid</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <Label className="text-xs">Quality</Label>
                                <Select
                                    value={settings.openaiSettings.quality}
                                    onValueChange={(value: "standard" | "hd") =>
                                        updateOpenAISetting("quality", value)
                                    }
                                >
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="standard">Standard</SelectItem>
                                        <SelectItem value="hd">HD</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Generation Settings */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Layers className="h-5 w-5" />
                        Generation Options
                    </CardTitle>
                    <CardDescription>
                        Configure how many images to generate and batch settings
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Multiple Images */}
                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <div className="text-sm font-medium">Enable Multiple Images</div>
                            <div className="text-sm text-muted-foreground">
                                Allow generating multiple images at once
                            </div>
                        </div>
                        <Switch
                            checked={settings.enableMultipleImages}
                            onCheckedChange={(checked) =>
                                updateSetting("enableMultipleImages", checked)
                            }
                        />
                    </div>

                    {settings.enableMultipleImages && (
                        <>
                            {/* Default Image Count */}
                            <div className="space-y-2">
                                <Label>
                                    Default Number of Images: {settings.defaultImageCount}
                                </Label>
                                <Slider
                                    value={[settings.defaultImageCount]}
                                    onValueChange={([value]) =>
                                        updateSetting("defaultImageCount", value)
                                    }
                                    max={settings.maxImageCount}
                                    min={1}
                                    step={1}
                                    className="w-full"
                                />
                            </div>

                            {/* Max Images Per Call */}
                            <div className="space-y-2">
                                <Label>Max Images Per API Call: {settings.maxImagesPerCall}</Label>
                                <Slider
                                    value={[settings.maxImagesPerCall]}
                                    onValueChange={([value]) =>
                                        updateSetting("maxImagesPerCall", value)
                                    }
                                    max={10}
                                    min={1}
                                    step={1}
                                    className="w-full"
                                />
                                <div className="text-xs text-muted-foreground">
                                    Higher values reduce API calls but may hit provider limits
                                </div>
                            </div>
                        </>
                    )}

                    {/* Batching */}
                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <div className="text-sm font-medium">Enable Smart Batching</div>
                            <div className="text-sm text-muted-foreground">
                                Automatically batch multiple image requests for efficiency
                            </div>
                        </div>
                        <Switch
                            checked={settings.enableBatching}
                            onCheckedChange={(checked) => updateSetting("enableBatching", checked)}
                        />
                    </div>
                </CardContent>
            </Card>

            {/* Size and Format Settings */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Image className="h-5 w-5" />
                        Size & Format
                    </CardTitle>
                    <CardDescription>
                        Configure default image dimensions and format preferences
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Prefer Aspect Ratio vs Size */}
                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <div className="text-sm font-medium">
                                Use Aspect Ratio (Recommended)
                            </div>
                            <div className="text-sm text-muted-foreground">
                                Use aspect ratios instead of fixed sizes for better model
                                compatibility
                            </div>
                        </div>
                        <Switch
                            checked={settings.preferAspectRatio}
                            onCheckedChange={(checked) =>
                                updateSetting("preferAspectRatio", checked)
                            }
                        />
                    </div>

                    {settings.preferAspectRatio ? (
                        <div className="space-y-2">
                            <Label>Default Aspect Ratio</Label>
                            <Select
                                value={settings.defaultAspectRatio}
                                onValueChange={(value: any) =>
                                    updateSetting("defaultAspectRatio", value)
                                }
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="1:1">1:1 (Square)</SelectItem>
                                    <SelectItem value="16:9">16:9 (Landscape)</SelectItem>
                                    <SelectItem value="9:16">9:16 (Portrait)</SelectItem>
                                    <SelectItem value="4:3">4:3 (Standard)</SelectItem>
                                    <SelectItem value="3:4">3:4 (Tall)</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    ) : (
                        <div className="space-y-2">
                            <Label>Default Size</Label>
                            <Select
                                value={settings.defaultSize}
                                onValueChange={(value: any) => updateSetting("defaultSize", value)}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="1024x1024">1024×1024 (Square)</SelectItem>
                                    <SelectItem value="1792x1024">1792×1024 (Landscape)</SelectItem>
                                    <SelectItem value="1024x1792">1024×1792 (Portrait)</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Advanced Settings */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Hash className="h-5 w-5" />
                        Advanced Options
                    </CardTitle>
                    <CardDescription>
                        Advanced features for reproducible and controlled generation
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Seed Settings */}
                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <div className="text-sm font-medium">Enable Seed Control</div>
                            <div className="text-sm text-muted-foreground">
                                Use seeds for reproducible image generation
                            </div>
                        </div>
                        <Switch
                            checked={settings.enableSeed}
                            onCheckedChange={(checked) => updateSetting("enableSeed", checked)}
                        />
                    </div>

                    {settings.enableSeed && (
                        <>
                            <div className="flex items-center justify-between">
                                <div className="space-y-0.5">
                                    <div className="text-sm font-medium">Allow Custom Seeds</div>
                                    <div className="text-sm text-muted-foreground">
                                        Let users specify their own seed values
                                    </div>
                                </div>
                                <Switch
                                    checked={settings.enableCustomSeed}
                                    onCheckedChange={(checked) =>
                                        updateSetting("enableCustomSeed", checked)
                                    }
                                />
                            </div>

                            {!settings.enableCustomSeed && (
                                <div className="space-y-2">
                                    <Label>Default Seed (optional)</Label>
                                    <Input
                                        type="number"
                                        placeholder="Leave empty for random"
                                        value={settings.defaultSeed || ""}
                                        onChange={(e) => {
                                            const value = e.target.value
                                                ? parseInt(e.target.value)
                                                : undefined;
                                            updateSetting("defaultSeed", value);
                                        }}
                                    />
                                </div>
                            )}
                        </>
                    )}

                    {/* Timeout Settings */}
                    <Separator />
                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <div className="text-sm font-medium">Enable Request Timeout</div>
                            <div className="text-sm text-muted-foreground">
                                Set maximum time to wait for image generation
                            </div>
                        </div>
                        <Switch
                            checked={settings.enableTimeout}
                            onCheckedChange={(checked) => updateSetting("enableTimeout", checked)}
                        />
                    </div>

                    {settings.enableTimeout && (
                        <div className="space-y-2">
                            <Label>Timeout (seconds): {settings.timeoutSeconds}</Label>
                            <Slider
                                value={[settings.timeoutSeconds]}
                                onValueChange={([value]) => updateSetting("timeoutSeconds", value)}
                                max={300}
                                min={10}
                                step={10}
                                className="w-full"
                            />
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Safety and Content Settings */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Shield className="h-5 w-5" />
                        Safety & Content
                    </CardTitle>
                    <CardDescription>Content filtering and safety check options</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <div className="text-sm font-medium">Content Filter</div>
                            <div className="text-sm text-muted-foreground">
                                Enable automatic content filtering for generated images
                            </div>
                        </div>
                        <Switch
                            checked={settings.enableContentFilter}
                            onCheckedChange={(checked) =>
                                updateSetting("enableContentFilter", checked)
                            }
                        />
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <div className="text-sm font-medium">Safety Check</div>
                            <div className="text-sm text-muted-foreground">
                                Perform safety checks on prompts before generation
                            </div>
                        </div>
                        <Switch
                            checked={settings.enableSafetyCheck}
                            onCheckedChange={(checked) =>
                                updateSetting("enableSafetyCheck", checked)
                            }
                        />
                    </div>

                    <Alert>
                        <Info className="h-4 w-4" />
                        <AlertDescription>
                            Safety features help ensure generated content complies with provider
                            policies and community guidelines.
                        </AlertDescription>
                    </Alert>
                </CardContent>
            </Card>

            {/* Storage and Export Settings */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Settings className="h-5 w-5" />
                        Storage & Export
                    </CardTitle>
                    <CardDescription>
                        Configure how generated images are saved and exported
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <div className="text-sm font-medium">Auto-Save Generated Images</div>
                            <div className="text-sm text-muted-foreground">
                                Automatically save generated images to local storage
                            </div>
                        </div>
                        <Switch
                            checked={settings.autoSaveGenerated}
                            onCheckedChange={(checked) =>
                                updateSetting("autoSaveGenerated", checked)
                            }
                        />
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <div className="text-sm font-medium">Compress Images</div>
                            <div className="text-sm text-muted-foreground">
                                Apply compression to reduce storage space
                            </div>
                        </div>
                        <Switch
                            checked={settings.compressImages}
                            onCheckedChange={(checked) => updateSetting("compressImages", checked)}
                        />
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <div className="text-sm font-medium">Include Generation Metadata</div>
                            <div className="text-sm text-muted-foreground">
                                Save prompt, model, and generation settings with images
                            </div>
                        </div>
                        <Switch
                            checked={settings.includeMetadata}
                            onCheckedChange={(checked) => updateSetting("includeMetadata", checked)}
                        />
                    </div>
                </CardContent>
            </Card>
        </div>
    );
};
