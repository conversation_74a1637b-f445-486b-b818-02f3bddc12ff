import { useSubscriptionAccess } from "@repo/common/hooks/use-subscription-access";
import { useApiKeysStore, useChatStore } from "@repo/common/store";
import { ChatMode } from "@repo/shared/config";
import { useSession } from "@repo/shared/lib/auth-client";
import { fireEvent, render, screen } from "@testing-library/react";
import { ImageModelSelector } from "../ImageModelSelector";

// Mock the hooks
jest.mock("@repo/common/store", () => ({
    useChatStore: jest.fn(),
    useApiKeysStore: jest.fn(),
}));

jest.mock("@repo/shared/lib/auth-client", () => ({
    useSession: jest.fn(),
}));

jest.mock("@repo/common/hooks/use-subscription-access", () => ({
    useSubscriptionAccess: jest.fn(),
}));

describe("ImageModelSelector", () => {
    // Setup default mocks
    beforeEach(() => {
        // Mock useChatStore
        (useChatStore as jest.Mock).mockImplementation(() => ({
            chatMode: ChatMode.GEMINI_IMAGEN_3_0,
            setChatMode: jest.fn(),
            getModelFromChatMode: jest.fn().mockReturnValue("imagen-3.0-generate-001"),
        }));

        // Mock useSession
        (useSession as jest.Mock).mockReturnValue({
            data: { user: { id: "test-user" } },
            status: "authenticated",
        });

        // Mock useSubscriptionAccess
        (useSubscriptionAccess as jest.Mock).mockReturnValue({
            canAccess: jest.fn().mockReturnValue(true),
        });

        // Mock useApiKeysStore
        (useApiKeysStore as jest.Mock).mockReturnValue({
            hasApiKeyForChatMode: jest.fn().mockReturnValue(true),
        });
    });

    it("renders the component with current model selected", () => {
        render(<ImageModelSelector />);
        expect(screen.getByText(/Google Imagen 3.0/i)).toBeInTheDocument();
    });

    it("shows login dialog when user is not signed in", () => {
        // Mock user not signed in
        (useSession as jest.Mock).mockReturnValue({
            data: null,
            status: "unauthenticated",
        });

        render(<ImageModelSelector />);

        // Open the select
        fireEvent.click(screen.getByRole("combobox"));

        // Select a model
        fireEvent.click(screen.getByText(/Gemini Imagen 3.0 Fast/i));

        // Check if login dialog is shown
        expect(
            screen.getByText(/Please log in to use image generation models/i),
        ).toBeInTheDocument();
    });

    it("shows BYOK dialog when API key is missing", () => {
        // Mock API key missing
        (useApiKeysStore as jest.Mock).mockReturnValue({
            hasApiKeyForChatMode: jest.fn().mockReturnValue(false),
        });

        render(<ImageModelSelector />);

        // Open the select
        fireEvent.click(screen.getByRole("combobox"));

        // Select a model
        fireEvent.click(screen.getByText(/Gemini Imagen 3.0 Fast/i));

        // Check if BYOK dialog is shown
        expect(screen.getByText(/API Key Required/i)).toBeInTheDocument();
    });

    it("calls onModelSelect when a model is selected", () => {
        const onModelSelect = jest.fn();
        render(<ImageModelSelector onModelSelect={onModelSelect} />);

        // Open the select
        fireEvent.click(screen.getByRole("combobox"));

        // Select a model
        fireEvent.click(screen.getByText(/Gemini Imagen 3.0 Fast/i));

        // Check if onModelSelect was called
        expect(onModelSelect).toHaveBeenCalledWith(ChatMode.GEMINI_IMAGEN_3_0_FAST);
    });
});
