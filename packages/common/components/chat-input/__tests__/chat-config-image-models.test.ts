import { ChatMode } from "@repo/shared/config";
import { describe, expect, it } from "vitest";
import { modelOptionsByProvider, getApiKeyForProvider } from "../chat-config";

describe("Chat Configuration - Image Models", () => {
    describe("modelOptionsByProvider", () => {
        it("should include image generation models in Google section", () => {
            const googleModels = modelOptionsByProvider.Google;
            
            expect(googleModels).toBeDefined();
            expect(Array.isArray(googleModels)).toBe(true);
            
            // Find image models
            const imagenModel = googleModels.find(m => m.value === ChatMode.GEMINI_IMAGEN_3_0);
            const imagenFastModel = googleModels.find(m => m.value === ChatMode.GEMINI_IMAGEN_3_0_FAST);
            
            expect(imagenModel).toBeDefined();
            expect(imagenFastModel).toBeDefined();
        });

        it("should configure Gemini Imagen 3.0 model correctly", () => {
            const googleModels = modelOptionsByProvider.Google;
            const imagenModel = googleModels.find(m => m.value === ChatMode.GEMINI_IMAGEN_3_0);
            
            expect(imagenModel).toMatchObject({
                label: "Gemini Imagen 3.0",
                value: ChatMode.GEMINI_IMAGEN_3_0,
                webSearch: false,
                requiredApiKey: "GEMINI_API_KEY",
                description: "Image generation",
            });
            
            // Should have an icon
            expect(imagenModel?.icon).toBeDefined();
        });

        it("should configure Gemini Imagen 3.0 Fast model correctly", () => {
            const googleModels = modelOptionsByProvider.Google;
            const imagenFastModel = googleModels.find(m => m.value === ChatMode.GEMINI_IMAGEN_3_0_FAST);
            
            expect(imagenFastModel).toMatchObject({
                label: "Gemini Imagen 3.0 Fast",
                value: ChatMode.GEMINI_IMAGEN_3_0_FAST,
                webSearch: false,
                requiredApiKey: "GEMINI_API_KEY",
                description: "Fast image generation",
            });
            
            // Should have an icon
            expect(imagenFastModel?.icon).toBeDefined();
        });

        it("should disable web search for image models", () => {
            const googleModels = modelOptionsByProvider.Google;
            const imageModels = googleModels.filter(m => 
                m.value === ChatMode.GEMINI_IMAGEN_3_0 || 
                m.value === ChatMode.GEMINI_IMAGEN_3_0_FAST
            );
            
            imageModels.forEach(model => {
                expect(model.webSearch).toBe(false);
            });
        });

        it("should require GEMINI_API_KEY for image models", () => {
            const googleModels = modelOptionsByProvider.Google;
            const imageModels = googleModels.filter(m => 
                m.value === ChatMode.GEMINI_IMAGEN_3_0 || 
                m.value === ChatMode.GEMINI_IMAGEN_3_0_FAST
            );
            
            imageModels.forEach(model => {
                expect(model.requiredApiKey).toBe("GEMINI_API_KEY");
            });
        });

        it("should have descriptive labels for image models", () => {
            const googleModels = modelOptionsByProvider.Google;
            const imageModels = googleModels.filter(m => 
                m.value === ChatMode.GEMINI_IMAGEN_3_0 || 
                m.value === ChatMode.GEMINI_IMAGEN_3_0_FAST
            );
            
            imageModels.forEach(model => {
                expect(model.label).toContain("Imagen");
                expect(model.label).toContain("3.0");
                expect(model.description).toBeTruthy();
                expect(model.description).toContain("generation");
            });
        });
    });

    describe("getApiKeyForProvider", () => {
        it("should return correct API key for Google provider", () => {
            const apiKey = getApiKeyForProvider("google");
            expect(apiKey).toBe("GEMINI_API_KEY");
        });

        it("should return correct API keys for all providers", () => {
            const providerKeyMap = {
                google: "GEMINI_API_KEY",
                openai: "OPENAI_API_KEY",
                anthropic: "ANTHROPIC_API_KEY",
                fireworks: "FIREWORKS_API_KEY",
                xai: "XAI_API_KEY",
                openrouter: "OPENROUTER_API_KEY",
            };

            Object.entries(providerKeyMap).forEach(([provider, expectedKey]) => {
                expect(getApiKeyForProvider(provider)).toBe(expectedKey);
            });
        });

        it("should return fallback for unknown providers", () => {
            const apiKey = getApiKeyForProvider("unknown-provider");
            expect(apiKey).toBe("BYOK_API_KEY");
        });
    });

    describe("Model Options Structure", () => {
        it("should have consistent structure across all model options", () => {
            const allModels = Object.values(modelOptionsByProvider).flat();
            
            allModels.forEach(model => {
                // Required fields
                expect(model.label).toBeTruthy();
                expect(model.value).toBeTruthy();
                expect(typeof model.webSearch).toBe("boolean");
                
                // Optional fields should be properly typed if present
                if (model.requiredApiKey) {
                    expect(typeof model.requiredApiKey).toBe("string");
                }
                if (model.description) {
                    expect(typeof model.description).toBe("string");
                }
                if (model.isFreeModel) {
                    expect(typeof model.isFreeModel).toBe("boolean");
                }
            });
        });

        it("should have unique values across all models", () => {
            const allModels = Object.values(modelOptionsByProvider).flat();
            const values = allModels.map(m => m.value);
            const uniqueValues = new Set(values);
            
            expect(uniqueValues.size).toBe(values.length);
        });

        it("should have unique labels within each provider", () => {
            Object.entries(modelOptionsByProvider).forEach(([provider, models]) => {
                const labels = models.map(m => m.label);
                const uniqueLabels = new Set(labels);
                
                expect(uniqueLabels.size).toBe(labels.length);
            });
        });
    });

    describe("Image Model Integration", () => {
        it("should place image models in the correct provider section", () => {
            const googleModels = modelOptionsByProvider.Google;
            const imageModelValues = [ChatMode.GEMINI_IMAGEN_3_0, ChatMode.GEMINI_IMAGEN_3_0_FAST];
            
            imageModelValues.forEach(value => {
                const model = googleModels.find(m => m.value === value);
                expect(model).toBeDefined();
            });
            
            // Should not be in other provider sections
            const otherProviders = Object.entries(modelOptionsByProvider)
                .filter(([provider]) => provider !== "Google");
            
            otherProviders.forEach(([provider, models]) => {
                imageModelValues.forEach(value => {
                    const model = models.find(m => m.value === value);
                    expect(model).toBeUndefined();
                });
            });
        });

        it("should maintain consistency with text models in the same provider", () => {
            const googleModels = modelOptionsByProvider.Google;
            const textModels = googleModels.filter(m => 
                m.value !== ChatMode.GEMINI_IMAGEN_3_0 && 
                m.value !== ChatMode.GEMINI_IMAGEN_3_0_FAST
            );
            const imageModels = googleModels.filter(m => 
                m.value === ChatMode.GEMINI_IMAGEN_3_0 || 
                m.value === ChatMode.GEMINI_IMAGEN_3_0_FAST
            );
            
            // All Google models should use the same API key
            const allGoogleModels = [...textModels, ...imageModels];
            allGoogleModels.forEach(model => {
                if (model.requiredApiKey) {
                    expect(model.requiredApiKey).toBe("GEMINI_API_KEY");
                }
            });
        });
    });

    describe("Model Configuration Validation", () => {
        it("should have valid ChatMode values for image models", () => {
            expect(ChatMode.GEMINI_IMAGEN_3_0).toBe("gemini-imagen-3.0");
            expect(ChatMode.GEMINI_IMAGEN_3_0_FAST).toBe("gemini-imagen-3.0-fast");
        });

        it("should have proper model ordering within Google section", () => {
            const googleModels = modelOptionsByProvider.Google;
            const modelLabels = googleModels.map(m => m.label);
            
            // Image models should be present
            expect(modelLabels).toContain("Gemini Imagen 3.0");
            expect(modelLabels).toContain("Gemini Imagen 3.0 Fast");
            
            // Should maintain logical ordering (regular before fast)
            const imagenIndex = modelLabels.indexOf("Gemini Imagen 3.0");
            const imagenFastIndex = modelLabels.indexOf("Gemini Imagen 3.0 Fast");
            
            expect(imagenIndex).toBeLessThan(imagenFastIndex);
        });
    });
});
