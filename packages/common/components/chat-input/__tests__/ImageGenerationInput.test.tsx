import { useChatStore } from "@repo/common/store";
import { ChatMode } from "@repo/shared/config";
import { useSession } from "@repo/shared/lib/auth-client";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { ImageGenerationInput } from "../ImageGenerationInput";

// Mock dependencies
jest.mock("@repo/common/store", () => ({
    useChatStore: jest.fn(),
    useApiKeysStore: jest.fn(() => ({
        hasApiKeyForChatMode: jest.fn(() => true),
    })),
}));

jest.mock("@repo/shared/lib/auth-client", () => ({
    useSession: jest.fn(),
}));

jest.mock("@repo/ai/models", () => ({
    getImageModel: jest.fn(() => ({
        id: "imagen-3.0-generate-001",
        name: "Gemini Imagen 3.0",
        provider: "google",
        type: "image",
        maxPromptLength: 2048,
        aspectRatios: ["1:1", "16:9", "9:16", "4:3", "3:4"],
    })),
}));

jest.mock("@repo/common/hooks/use-subscription-access", () => ({
    useSubscriptionAccess: jest.fn(() => ({
        canAccess: jest.fn(() => true),
    })),
}));

describe("ImageGenerationInput", () => {
    const mockOnGenerate = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();

        // Default mock implementations
        (useChatStore as jest.Mock).mockImplementation(() => ({
            chatMode: ChatMode.GEMINI_IMAGEN_3_0,
        }));

        (useSession as jest.Mock).mockImplementation(() => ({
            data: { user: { id: "test-user" } },
        }));
    });

    it("renders the image generation input", () => {
        render(<ImageGenerationInput onGenerate={mockOnGenerate} />);

        expect(
            screen.getByPlaceholderText("Describe the image you want to generate..."),
        ).toBeInTheDocument();
        expect(screen.getByText("Image Generation")).toBeInTheDocument();
        expect(screen.getByRole("button", { name: /generate/i })).toBeInTheDocument();
    });

    it("disables the generate button when prompt is empty", () => {
        render(<ImageGenerationInput onGenerate={mockOnGenerate} />);

        const generateButton = screen.getByRole("button", { name: /generate/i });
        expect(generateButton).toBeDisabled();
    });

    it("enables the generate button when prompt is valid", () => {
        render(<ImageGenerationInput onGenerate={mockOnGenerate} />);

        const promptInput = screen.getByPlaceholderText(
            "Describe the image you want to generate...",
        );
        fireEvent.change(promptInput, { target: { value: "A beautiful sunset over mountains" } });

        const generateButton = screen.getByRole("button", { name: /generate/i });
        expect(generateButton).not.toBeDisabled();
    });

    it("calls onGenerate with prompt and aspect ratio when generate button is clicked", async () => {
        render(<ImageGenerationInput onGenerate={mockOnGenerate} />);

        const promptInput = screen.getByPlaceholderText(
            "Describe the image you want to generate...",
        );
        fireEvent.change(promptInput, { target: { value: "A beautiful sunset over mountains" } });

        const generateButton = screen.getByRole("button", { name: /generate/i });
        fireEvent.click(generateButton);

        await waitFor(() => {
            expect(mockOnGenerate).toHaveBeenCalledWith("A beautiful sunset over mountains", "1:1");
        });
    });

    it("shows loading state when isGenerating is true", () => {
        render(<ImageGenerationInput onGenerate={mockOnGenerate} isGenerating={true} />);

        expect(screen.getByText("Generating...")).toBeInTheDocument();
        const generateButton = screen.getByRole("button", { name: /generating/i });
        expect(generateButton).toBeDisabled();
    });

    it("shows login dialog when user is not signed in", () => {
        (useSession as jest.Mock).mockImplementation(() => ({
            data: null,
        }));

        render(<ImageGenerationInput onGenerate={mockOnGenerate} />);

        const promptInput = screen.getByPlaceholderText(
            "Describe the image you want to generate...",
        );
        fireEvent.change(promptInput, { target: { value: "A beautiful sunset over mountains" } });

        const generateButton = screen.getByRole("button", { name: /generate/i });
        fireEvent.click(generateButton);

        expect(screen.getByText("Login Required")).toBeInTheDocument();
    });
});
