"use client";

import { getImageModel, getImageModels, type ImageModel } from "@repo/ai/models";
import { useSubscriptionAccess } from "@repo/common/hooks/use-subscription-access";
import { useApiKeysStore, useChatStore } from "@repo/common/store";
import { ChatMode, ChatModeConfig, getChatModeName } from "@repo/shared/config";
import { useSession } from "@repo/shared/lib/auth-client";
import { FeatureSlug } from "@repo/shared/types/subscription";
import { Badge, Button, Flex, ScrollArea, Select, Text, Tooltip } from "@repo/ui";
import { Gift, Image, Info } from "lucide-react";
import { useState } from "react";
import { BYOKValidationDialog } from "../byok-validation-dialog";
import { LoginRequiredDialog } from "../login-required-dialog";

export interface ImageModelSelectorProps {
    onModelSelect?: (chatMode: ChatMode) => void;
    className?: string;
}

export const ImageModelSelector = ({ onModelSelect, className = "" }: ImageModelSelectorProps) => {
    const [showLoginPrompt, setShowLoginPrompt] = useState(false);
    const [showBYOKDialog, setShowBYOKDialog] = useState(false);
    const [selectedChatMode, setSelectedChatMode] = useState<ChatMode | null>(null);

    const { chatMode, setChatMode } = useChatStore();
    const { data: session } = useSession();
    const isSignedIn = !!session;
    const { canAccess } = useSubscriptionAccess();
    const { hasApiKeyForChatMode } = useApiKeysStore();

    // Get all available image models
    const imageModels = getImageModels();

    // Check if the current model is an image model
    const currentModel = getImageModel(useChatStore((state) => state.getModelFromChatMode()));
    const isImageModeActive = !!currentModel;

    // Create model options for the selector
    const imageModelOptions = imageModels
        .map((model) => {
            // Map image model to chat mode
            const modelChatMode = mapImageModelToChatMode(model);
            if (!modelChatMode) return null;

            // Check if model requires API key
            const requiresApiKey = !model.isFree;
            const hasApiKey = hasApiKeyForChatMode(
                modelChatMode,
                isSignedIn,
                canAccess(FeatureSlug.VT_PLUS),
            );

            // Check if model requires VT+
            const chatModeConfig = ChatModeConfig[modelChatMode];
            const requiresVTPlus = chatModeConfig?.requiredPlan || chatModeConfig?.requiredFeature;
            const hasVTPlus = requiresVTPlus
                ? canAccess(chatModeConfig.requiredFeature || FeatureSlug.VT_PLUS)
                : true;

            return {
                label: model.name,
                value: modelChatMode,
                description: getModelDescription(model),
                icon: model.isFree ? (
                    <Gift className="text-green-500" size={16} />
                ) : (
                    <Image className="text-blue-500" size={16} />
                ),
                disabled: requiresApiKey && !hasApiKey,
                requiresApiKey,
                hasApiKey,
                requiresVTPlus,
                hasVTPlus,
            };
        })
        .filter(Boolean);

    const handleModelSelect = (selectedMode: ChatMode) => {
        // Check if user is signed in
        if (!isSignedIn) {
            setShowLoginPrompt(true);
            return;
        }

        // Check if user has valid API key for the selected chat mode
        if (!hasApiKeyForChatMode(selectedMode, isSignedIn, canAccess(FeatureSlug.VT_PLUS))) {
            setSelectedChatMode(selectedMode);
            setShowBYOKDialog(true);
            return;
        }

        // Update chat mode
        setChatMode(selectedMode);
        if (onModelSelect) {
            onModelSelect(selectedMode);
        }
    };

    const handleApiKeySet = () => {
        if (selectedChatMode) {
            setChatMode(selectedChatMode);
            if (onModelSelect) {
                onModelSelect(selectedChatMode);
            }
            setSelectedChatMode(null);
        }
        setShowBYOKDialog(false);
    };

    return (
        <>
            <Select value={chatMode} onValueChange={handleModelSelect} className={className}>
                <Select.Trigger className="w-full">
                    <Select.Value placeholder="Select Image Model">
                        <Flex className="items-center gap-2">
                            <Image size={16} className="text-blue-500" />
                            <Text>{getChatModeName(chatMode)}</Text>
                        </Flex>
                    </Select.Value>
                </Select.Trigger>
                <Select.Content>
                    <ScrollArea className="h-[300px]">
                        {imageModelOptions.map((option) => (
                            <Select.Item
                                key={option.value}
                                value={option.value}
                                disabled={option.disabled}
                            >
                                <Flex className="items-center justify-between w-full">
                                    <Flex className="items-center gap-2">
                                        {option.icon}
                                        <Text>{option.label}</Text>
                                    </Flex>
                                    <Flex className="items-center gap-1">
                                        {option.requiresApiKey && !option.hasApiKey && (
                                            <Badge variant="outline" className="text-xs">
                                                API Key Required
                                            </Badge>
                                        )}
                                        {option.requiresVTPlus && !option.hasVTPlus && (
                                            <Badge
                                                variant="outline"
                                                className="text-xs bg-purple-100 text-purple-800"
                                            >
                                                VT+
                                            </Badge>
                                        )}
                                        <Tooltip content={option.description}>
                                            <Button variant="ghost" size="icon" className="h-6 w-6">
                                                <Info size={14} />
                                            </Button>
                                        </Tooltip>
                                    </Flex>
                                </Flex>
                            </Select.Item>
                        ))}
                    </ScrollArea>
                </Select.Content>
            </Select>

            {/* Login prompt dialog */}
            <LoginRequiredDialog
                description="Please log in to use image generation models."
                icon={Image}
                isOpen={showLoginPrompt}
                onClose={() => setShowLoginPrompt(false)}
                title="Login Required"
            />

            {/* BYOK dialog */}
            {showBYOKDialog && selectedChatMode && (
                <BYOKValidationDialog
                    chatMode={selectedChatMode}
                    isOpen={showBYOKDialog}
                    onApiKeySet={handleApiKeySet}
                    onClose={() => {
                        setShowBYOKDialog(false);
                        setSelectedChatMode(null);
                    }}
                />
            )}
        </>
    );
};

// Helper function to map image model to chat mode
function mapImageModelToChatMode(model: ImageModel): ChatMode | null {
    switch (model.id) {
        case "imagen-3.0-generate-001":
            return ChatMode.GEMINI_IMAGEN_3_0;
        case "imagen-3.0-fast-generate-001":
            return ChatMode.GEMINI_IMAGEN_3_0_FAST;
        default:
            return null;
    }
}

// Helper function to get model description
function getModelDescription(model: ImageModel): string {
    switch (model.id) {
        case "imagen-3.0-generate-001":
            return "High-quality image generation model with detailed outputs. Requires Google API key.";
        case "imagen-3.0-fast-generate-001":
            return "Faster image generation model with good quality. Requires Google API key.";
        default:
            return "Image generation model";
    }
}
