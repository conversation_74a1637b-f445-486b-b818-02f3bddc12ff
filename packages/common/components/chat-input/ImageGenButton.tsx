"use client";

import { getModelFromChatMode, supportsImageOutput } from "@repo/ai/models";
import { useChatStore } from "@repo/common/store";
import { ChatMode } from "@repo/shared/config";
import { Button } from "@repo/ui";
import { Image, Sparkles } from "lucide-react";
import { useState } from "react";

export function ImageGenButton() {
    const { chatMode } = useChatStore();
    const [isHovered, setIsHovered] = useState(false);

    // Check if the current model supports image output via responseModalities
    const model = getModelFromChatMode(chatMode);
    const supportsImageGen = model && supportsImageOutput(model);

    // Only show the button for models that support image output
    if (!supportsImageGen || chatMode !== ChatMode.GEMINI_2_0_FLASH_EXP) {
        return null;
    }

    const handleImageGeneration = () => {
        // This button serves as a visual indicator that the model can generate images
        // The actual image generation happens through the normal chat flow
        // We could add a tooltip or modal here to explain how to use image generation
    };

    return (
        <Button
            size="xs"
            variant="secondary"
            className="border border-muted-foreground/30 bg-gradient-to-r from-blue-50 to-purple-50 hover:from-blue-100 hover:to-purple-100 dark:from-blue-950/20 dark:to-purple-950/20 dark:hover:from-blue-900/30 dark:hover:to-purple-900/30"
            onClick={handleImageGeneration}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            title="Image Generation Available - This model can generate images! Just ask for images in your message."
        >
            <div className="flex items-center gap-1.5">
                {isHovered ? (
                    <Sparkles className="text-purple-500" size={14} />
                ) : (
                    <Image className="text-blue-500" size={14} />
                )}
                <span className="text-xs font-medium bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent dark:from-blue-400 dark:to-purple-400">
                    Image Gen
                </span>
            </div>
        </Button>
    );
}
