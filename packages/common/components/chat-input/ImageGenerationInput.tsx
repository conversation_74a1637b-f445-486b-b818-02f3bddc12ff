"use client";

import { getImageModel } from "@repo/ai/models";
import { useSubscriptionAccess } from "@repo/common/hooks/use-subscription-access";
import { useApiKeysStore, useChatStore } from "@repo/common/store";
import { ChatModeConfig } from "@repo/shared/config";
import { useSession } from "@repo/shared/lib/auth-client";
import { FeatureSlug } from "@repo/shared/types/subscription";
import { Button, Flex, Input, Select, Tooltip } from "@repo/ui";
import { AnimatePresence, motion } from "framer-motion";
import { Image, Loader2, Send } from "lucide-react";
import { useState } from "react";
import { BYOKValidationDialog } from "../byok-validation-dialog";
import { GatedFeatureAlert } from "../gated-feature-alert";
import { LoginRequiredDialog } from "../login-required-dialog";

export interface ImageGenerationInputProps {
    onGenerate: (prompt: string, aspectRatio: string) => Promise<void>;
    isGenerating?: boolean;
}

export const ImageGenerationInput = ({
    onGenerate,
    isGenerating = false,
}: ImageGenerationInputProps) => {
    const [prompt, setPrompt] = useState("");
    const [aspectRatio, setAspectRatio] = useState("1:1");
    const [showLoginPrompt, setShowLoginPrompt] = useState(false);
    const [showBYOKDialog, setShowBYOKDialog] = useState(false);
    const [pendingGeneration, setPendingGeneration] = useState<(() => void) | null>(null);

    const chatMode = useChatStore((state) => state.chatMode);
    const { data: session } = useSession();
    const isSignedIn = !!session;
    const { canAccess } = useSubscriptionAccess();
    const { hasApiKeyForChatMode } = useApiKeysStore();

    // Get the image model details based on the current chat mode
    const imageModel = getImageModel(useChatStore((state) => getImageModel(chatMode)));

    // Check if the prompt is valid (not empty and within length limits)
    const isPromptValid =
        prompt.trim().length > 0 && (!imageModel || prompt.length <= imageModel.maxPromptLength);

    // Get available aspect ratios from the model
    const aspectRatios = imageModel?.aspectRatios || ["1:1", "16:9", "9:16", "4:3", "3:4"];

    // Check if the current chat mode requires VT+
    const chatModeConfig = ChatModeConfig[chatMode];
    const requiresVTPlus = chatModeConfig?.requiredPlan || chatModeConfig?.requiredFeature;

    const handleApiKeySet = () => {
        // Clear the pending generation and execute
        setPendingGeneration(null);
        handleGenerate();
    };

    const handleGenerate = async () => {
        if (!isPromptValid || isGenerating) return;

        // Check if user is signed in
        if (!isSignedIn) {
            setShowLoginPrompt(true);
            return;
        }

        // Check if user has valid API key for the selected chat mode
        if (!hasApiKeyForChatMode(chatMode, isSignedIn, canAccess(FeatureSlug.VT_PLUS))) {
            setPendingGeneration(() => handleGenerate);
            setShowBYOKDialog(true);
            return;
        }

        // Check subscription requirements for current chat mode
        if (requiresVTPlus) {
            const requiredFeature = chatModeConfig?.requiredFeature;
            const hasAccess = requiredFeature
                ? canAccess(requiredFeature)
                : canAccess(FeatureSlug.ADVANCED_CHAT_MODES);

            if (!hasAccess) {
                // Let GatedFeatureAlert handle this
                return;
            }
        }

        // All checks passed, generate the image
        await onGenerate(prompt, aspectRatio);
    };

    const renderGenerationInput = () => (
        <AnimatePresence>
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="w-full"
                initial={{ opacity: 0, y: 10 }}
                transition={{ duration: 0.2, ease: "easeOut" }}
            >
                <Flex className="w-full flex-col gap-3 p-3">
                    <Flex className="w-full items-center gap-2">
                        <Image size={20} className="text-primary" />
                        <Type className="font-medium">Image Generation</Type>
                    </Flex>

                    <Input
                        className="w-full"
                        placeholder="Describe the image you want to generate..."
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        maxLength={imageModel?.maxPromptLength || 2048}
                        aria-label="Image generation prompt"
                        aria-describedby="prompt-help character-count"
                        onKeyDown={(e) => {
                            if (e.key === "Enter" && !e.shiftKey) {
                                e.preventDefault();
                                handleGenerate();
                            }
                        }}
                    />

                    <Flex className="w-full items-center justify-between">
                        <Select
                            value={aspectRatio}
                            onValueChange={setAspectRatio}
                            disabled={isGenerating}
                        >
                            <Select.Trigger
                                className="w-32"
                                aria-label="Select aspect ratio for generated image"
                            >
                                <Select.Value placeholder="Aspect Ratio" />
                            </Select.Trigger>
                            <Select.Content>
                                {aspectRatios.map((ratio) => (
                                    <Select.Item
                                        key={ratio}
                                        value={ratio}
                                        aria-label={`Aspect ratio ${ratio}`}
                                    >
                                        {ratio}
                                    </Select.Item>
                                ))}
                            </Select.Content>
                        </Select>

                        <Tooltip
                            content={
                                isPromptValid ? "Generate Image" : "Please enter a valid prompt"
                            }
                        >
                            <Button
                                onClick={handleGenerate}
                                disabled={!isPromptValid || isGenerating}
                                className="gap-2"
                                aria-label={
                                    isGenerating
                                        ? "Generating image, please wait..."
                                        : isPromptValid
                                          ? "Generate image from prompt"
                                          : "Enter a valid prompt to generate image"
                                }
                                aria-describedby="prompt-help"
                            >
                                {isGenerating ? (
                                    <>
                                        <Loader2
                                            className="h-4 w-4 animate-spin"
                                            aria-hidden="true"
                                        />
                                        Generating...
                                    </>
                                ) : (
                                    <>
                                        <Send className="h-4 w-4" aria-hidden="true" />
                                        Generate
                                    </>
                                )}
                            </Button>
                        </Tooltip>
                    </Flex>

                    {imageModel && (
                        <Text
                            className="text-xs text-muted-foreground"
                            id="character-count"
                            aria-live="polite"
                        >
                            {prompt.length}/{imageModel.maxPromptLength} characters
                        </Text>
                    )}

                    {/* Hidden help text for screen readers */}
                    <div id="prompt-help" className="sr-only">
                        Enter a detailed description of the image you want to generate. Be specific
                        about colors, style, composition, and any other visual elements. Maximum{" "}
                        {imageModel?.maxPromptLength || 2048} characters.
                    </div>
                </Flex>
            </motion.div>
        </AnimatePresence>
    );

    // If chat mode requires VT+ subscription, wrap with GatedFeatureAlert
    const generationInput =
        requiresVTPlus && isSignedIn ? (
            <GatedFeatureAlert
                message="Image generation with this model is a VT+ feature. Upgrade to access enhanced AI capabilities."
                requiredFeature={chatModeConfig?.requiredFeature}
                requiredPlan={chatModeConfig?.requiredPlan}
                title="Image Generation Requires VT+"
            >
                {renderGenerationInput()}
            </GatedFeatureAlert>
        ) : (
            renderGenerationInput()
        );

    return (
        <>
            {generationInput}

            {/* Login prompt dialog */}
            <LoginRequiredDialog
                description="Please log in to generate images."
                icon={Image}
                isOpen={showLoginPrompt}
                onClose={() => setShowLoginPrompt(false)}
                title="Login Required"
            />

            {/* BYOK dialog */}
            {showBYOKDialog && (
                <BYOKValidationDialog
                    chatMode={chatMode}
                    isOpen={showBYOKDialog}
                    onApiKeySet={handleApiKeySet}
                    onClose={() => {
                        setShowBYOKDialog(false);
                        setPendingGeneration(null);
                    }}
                />
            )}
        </>
    );
};
