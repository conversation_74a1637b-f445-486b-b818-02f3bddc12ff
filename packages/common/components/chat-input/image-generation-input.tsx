"use client";

import { But<PERSON>, Textarea } from "@repo/ui";
import { <PERSON>I<PERSON>, Sparkles } from "lucide-react";
import { useState } from "react";

interface ImageGenerationInputProps {
    onGenerate: (prompt: string, aspectRatio?: string) => Promise<void>;
    isGenerating: boolean;
}

const ASPECT_RATIOS = [
    { value: "1:1", label: "Square", icon: "⬜" },
    { value: "16:9", label: "Landscape", icon: "▭" },
    { value: "9:16", label: "Portrait", icon: "▯" },
    { value: "4:3", label: "Standard", icon: "▬" },
    { value: "3:4", label: "Tall", icon: "▮" },
];

export const ImageGenerationInput = ({ onGenerate, isGenerating }: ImageGenerationInputProps) => {
    const [prompt, setPrompt] = useState("");
    const [selectedAspectRatio, setSelectedAspectRatio] = useState("1:1");

    const handleGenerate = async () => {
        if (!prompt.trim() || isGenerating) return;

        await onGenerate(prompt.trim(), selectedAspectRatio);
        setPrompt("");
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            handleGenerate();
        }
    };

    return (
        <div className="flex flex-col gap-3 p-3">
            {/* Prompt Input */}
            <div className="relative">
                <Textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Describe the image you want to generate..."
                    className="min-h-[80px] resize-none pr-12"
                    disabled={isGenerating}
                    maxLength={2048}
                />
                <div className="absolute bottom-2 right-2 flex items-center gap-1">
                    <ImageIcon size={16} className="text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">{prompt.length}/2048</span>
                </div>
            </div>

            {/* Aspect Ratio Selection */}
            <div className="flex flex-col gap-2">
                <label className="text-sm font-medium text-foreground">Aspect Ratio</label>
                <div className="flex flex-wrap gap-2">
                    {ASPECT_RATIOS.map((ratio) => (
                        <Button
                            key={ratio.value}
                            variant={selectedAspectRatio === ratio.value ? "default" : "outline"}
                            size="sm"
                            onClick={() => setSelectedAspectRatio(ratio.value)}
                            disabled={isGenerating}
                            className="flex items-center gap-2"
                        >
                            <span>{ratio.icon}</span>
                            <span>{ratio.label}</span>
                            <span className="text-xs opacity-70">({ratio.value})</span>
                        </Button>
                    ))}
                </div>
            </div>

            {/* Generate Button */}
            <Button
                onClick={handleGenerate}
                disabled={!prompt.trim() || isGenerating}
                className="w-full"
                size="lg"
            >
                {isGenerating ? (
                    <>
                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                        Generating...
                    </>
                ) : (
                    <>
                        <Sparkles size={16} className="mr-2" />
                        Generate Image
                    </>
                )}
            </Button>
        </div>
    );
};
