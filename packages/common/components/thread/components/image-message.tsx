"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogTrigger } from "@repo/ui";
import { CornerDownRight, Download, Expand } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

interface ImageMessageProps {
    imageAttachment: string;
    isGenerated?: boolean;
    prompt?: string;
}

export const ImageMessage = ({
    imageAttachment,
    isGenerated = false,
    prompt,
}: ImageMessageProps) => {
    const [isFullscreen, setIsFullscreen] = useState(false);

    const handleDownload = () => {
        const link = document.createElement("a");
        link.href = imageAttachment;
        link.download = `generated-image-${Date.now()}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    if (isGenerated) {
        // Full-size display for generated images
        return (
            <div className="relative max-w-md rounded-lg overflow-hidden shadow-lg">
                <Image
                    alt={prompt || "Generated image"}
                    className="w-full h-auto rounded-lg"
                    src={imageAttachment}
                    width={512}
                    height={512}
                    priority
                />

                {/* Image overlay with actions */}
                <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-200 group">
                    <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <Dialog open={isFullscreen} onOpenChange={setIsFullscreen}>
                            <DialogTrigger asChild>
                                <Button
                                    size="icon-sm"
                                    variant="secondary"
                                    className="bg-black/50 hover:bg-black/70 text-white border-none"
                                >
                                    <Expand size={14} />
                                </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-4xl max-h-[90vh] p-0">
                                <div className="relative">
                                    <Image
                                        alt={prompt || "Generated image"}
                                        className="w-full h-auto"
                                        src={imageAttachment}
                                        width={1024}
                                        height={1024}
                                        priority
                                    />
                                    {prompt && (
                                        <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white p-4">
                                            <p className="text-sm">{prompt}</p>
                                        </div>
                                    )}
                                </div>
                            </DialogContent>
                        </Dialog>

                        <Button
                            size="icon-sm"
                            variant="secondary"
                            className="bg-black/50 hover:bg-black/70 text-white border-none"
                            onClick={handleDownload}
                        >
                            <Download size={14} />
                        </Button>
                    </div>
                </div>

                {/* Prompt display */}
                {prompt && (
                    <div className="mt-2 p-2 bg-muted rounded text-sm text-muted-foreground">
                        <strong>Prompt:</strong> {prompt}
                    </div>
                )}
            </div>
        );
    }

    // Original small thumbnail display for uploaded images
    return (
        <div className="flex flex-row items-center gap-2 p-1">
            <CornerDownRight className="text-muted-foreground/50" size={16} />
            <div className="relative flex w-12 flex-row items-center gap-2 ">
                <Image
                    alt="image"
                    className="relative inset-0 rounded-lg"
                    src={imageAttachment}
                    width={48}
                    height={48}
                />
            </div>
        </div>
    );
};
