"use client";

import { log } from "@repo/shared/logger";
import { <PERSON><PERSON>, Label, Textarea } from "@repo/ui";
import { <PERSON>, <PERSON>O<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Volume2 } from "lucide-react";
import { useEffect, useState } from "react";

/**
 * Interface for image accessibility features
 */
export interface ImageAccessibilityProps {
    imageUrl?: string;
    imageData?: Uint8Array;
    prompt: string;
    generatedAltText?: string;
    onAltTextChange?: (altText: string) => void;
    onAltTextGenerate?: () => Promise<string>;
    className?: string;
}

/**
 * Generate alt text for an image based on its prompt and visual analysis
 */
export const generateAltText = async (prompt: string, imageData?: Uint8Array): Promise<string> => {
    try {
        // For now, generate alt text based on the prompt
        // In the future, this could use vision models to analyze the actual image
        const baseAltText = `Generated image: ${prompt}`;

        // Add contextual information
        const contextualElements = [];

        // Analyze prompt for common elements
        if (prompt.toLowerCase().includes("person") || prompt.toLowerCase().includes("people")) {
            contextualElements.push("featuring people");
        }
        if (prompt.toLowerCase().includes("landscape") || prompt.toLowerCase().includes("nature")) {
            contextualElements.push("landscape scene");
        }
        if (prompt.toLowerCase().includes("abstract")) {
            contextualElements.push("abstract artwork");
        }
        if (prompt.toLowerCase().includes("portrait")) {
            contextualElements.push("portrait style");
        }

        let enhancedAltText = baseAltText;
        if (contextualElements.length > 0) {
            enhancedAltText += ` (${contextualElements.join(", ")})`;
        }

        log.info("Generated alt text for image", {
            prompt: prompt.substring(0, 50),
            altText: enhancedAltText.substring(0, 100),
        });

        return enhancedAltText;
    } catch (error) {
        log.error("Failed to generate alt text", { error });
        return `Generated image: ${prompt}`;
    }
};

/**
 * Component for managing image accessibility features
 */
export const ImageAccessibilityControls = ({
    imageUrl,
    imageData,
    prompt,
    generatedAltText,
    onAltTextChange,
    onAltTextGenerate,
    className = "",
}: ImageAccessibilityProps) => {
    const [altText, setAltText] = useState(generatedAltText || "");
    const [isGeneratingAltText, setIsGeneratingAltText] = useState(false);
    const [showAltTextEditor, setShowAltTextEditor] = useState(false);
    const [isListening, setIsListening] = useState(false);

    // Auto-generate alt text when component mounts
    useEffect(() => {
        if (!altText && prompt) {
            handleGenerateAltText();
        }
    }, [prompt]);

    /**
     * Generate alt text for the image
     */
    const handleGenerateAltText = async () => {
        if (!prompt) return;

        try {
            setIsGeneratingAltText(true);

            let newAltText: string;
            if (onAltTextGenerate) {
                newAltText = await onAltTextGenerate();
            } else {
                newAltText = await generateAltText(prompt, imageData);
            }

            setAltText(newAltText);
            onAltTextChange?.(newAltText);
        } catch (error) {
            log.error("Failed to generate alt text", { error });
        } finally {
            setIsGeneratingAltText(false);
        }
    };

    /**
     * Handle alt text changes
     */
    const handleAltTextChange = (newAltText: string) => {
        setAltText(newAltText);
        onAltTextChange?.(newAltText);
    };

    /**
     * Start voice input for alt text (if supported)
     */
    const startVoiceInput = () => {
        if (!("webkitSpeechRecognition" in window) && !("SpeechRecognition" in window)) {
            log.warn("Speech recognition not supported");
            return;
        }

        try {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            const recognition = new SpeechRecognition();

            recognition.continuous = false;
            recognition.interimResults = false;
            recognition.lang = "en-US";

            recognition.onstart = () => {
                setIsListening(true);
            };

            recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                handleAltTextChange(transcript);
            };

            recognition.onerror = (event) => {
                log.error("Speech recognition error", { error: event.error });
                setIsListening(false);
            };

            recognition.onend = () => {
                setIsListening(false);
            };

            recognition.start();
        } catch (error) {
            log.error("Failed to start voice input", { error });
        }
    };

    /**
     * Read alt text aloud (if supported)
     */
    const readAltText = () => {
        if (!altText || !("speechSynthesis" in window)) {
            log.warn("Speech synthesis not supported");
            return;
        }

        try {
            const utterance = new SpeechSynthesisUtterance(altText);
            utterance.rate = 0.8;
            utterance.pitch = 1;
            utterance.volume = 0.8;

            speechSynthesis.speak(utterance);
        } catch (error) {
            log.error("Failed to read alt text", { error });
        }
    };

    return (
        <div className={`space-y-3 ${className}`}>
            {/* Alt text display/editor toggle */}
            <div className="flex items-center justify-between">
                <Label htmlFor="alt-text" className="text-sm font-medium">
                    Image Description
                </Label>
                <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAltTextEditor(!showAltTextEditor)}
                    aria-label={showAltTextEditor ? "Hide alt text editor" : "Show alt text editor"}
                >
                    {showAltTextEditor ? <EyeOff size={16} /> : <Eye size={16} />}
                    {showAltTextEditor ? "Hide" : "Edit"}
                </Button>
            </div>

            {/* Alt text display */}
            {!showAltTextEditor && altText && (
                <div className="p-3 bg-muted/50 rounded-lg border">
                    <p className="text-sm text-muted-foreground" id="alt-text-display">
                        {altText}
                    </p>
                    <div className="flex gap-2 mt-2">
                        <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={readAltText}
                            aria-label="Read image description aloud"
                        >
                            <Volume2 size={14} />
                            Read Aloud
                        </Button>
                    </div>
                </div>
            )}

            {/* Alt text editor */}
            {showAltTextEditor && (
                <div className="space-y-3">
                    <Textarea
                        id="alt-text"
                        value={altText}
                        onChange={(e) => handleAltTextChange(e.target.value)}
                        placeholder="Describe what's in this image for screen readers..."
                        className="min-h-[80px]"
                        aria-describedby="alt-text-help"
                    />

                    <div className="flex gap-2 flex-wrap">
                        <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={handleGenerateAltText}
                            disabled={isGeneratingAltText || !prompt}
                            aria-label="Generate automatic image description"
                        >
                            {isGeneratingAltText ? "Generating..." : "Auto-Generate"}
                        </Button>

                        {("webkitSpeechRecognition" in window || "SpeechRecognition" in window) && (
                            <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={startVoiceInput}
                                disabled={isListening}
                                aria-label={
                                    isListening ? "Listening for voice input..." : "Use voice input"
                                }
                            >
                                {isListening ? (
                                    <Mic className="animate-pulse" size={14} />
                                ) : (
                                    <MicOff size={14} />
                                )}
                                {isListening ? "Listening..." : "Voice Input"}
                            </Button>
                        )}

                        {altText && "speechSynthesis" in window && (
                            <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={readAltText}
                                aria-label="Read image description aloud"
                            >
                                <Volume2 size={14} />
                                Read Aloud
                            </Button>
                        )}
                    </div>

                    <p id="alt-text-help" className="text-xs text-muted-foreground">
                        Provide a clear, concise description of the image content for users who
                        cannot see it. Good alt text describes the essential visual information
                        without being overly detailed.
                    </p>
                </div>
            )}
        </div>
    );
};

/**
 * Hook for managing image accessibility state
 */
export const useImageAccessibility = (prompt: string, imageData?: Uint8Array) => {
    const [altText, setAltText] = useState("");
    const [isGeneratingAltText, setIsGeneratingAltText] = useState(false);

    const generateImageAltText = async (): Promise<string> => {
        setIsGeneratingAltText(true);
        try {
            const generated = await generateAltText(prompt, imageData);
            setAltText(generated);
            return generated;
        } finally {
            setIsGeneratingAltText(false);
        }
    };

    useEffect(() => {
        if (prompt && !altText) {
            generateImageAltText();
        }
    }, [prompt]);

    return {
        altText,
        setAltText,
        isGeneratingAltText,
        generateImageAltText,
    };
};
