import React, { useState, useEffect } from "react";
import {
    Alert,
    AlertDescription,
    Button,
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
    Switch,
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    TypographyMuted,
} from "@repo/ui";
import { 
    Image, 
    Trash2, 
    Download, 
    AlertCircle, 
    HardDrive, 
    RefreshCw,
    Settings,
    Shield
} from "lucide-react";
import { log } from "@repo/shared/logger";
import {
    getImageStorageStats,
    clearAllImages,
    cleanupExpiredImages,
    cleanupTemporaryImages,
    type ImageStorageStats,
} from "../lib/storage/image-storage";
import { useAppStore } from "../store";

/**
 * Format bytes to human readable format
 */
const formatBytes = (bytes: number): string => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
};

/**
 * Image storage settings component
 */
export const ImageStorageSettings = () => {
    const [stats, setStats] = useState<ImageStorageStats | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [isClearing, setIsClearing] = useState(false);
    const [isCleaningUp, setIsCleaningUp] = useState(false);

    // Settings from app store
    const imageStorageSettings = useAppStore((state) => state.imageStorageSettings);
    const setImageStorageSettings = useAppStore((state) => state.setImageStorageSettings);

    /**
     * Load storage statistics
     */
    const loadStats = async () => {
        try {
            setIsLoading(true);
            const storageStats = await getImageStorageStats();
            setStats(storageStats);
        } catch (error) {
            log.error("Failed to load image storage stats", { error });
        } finally {
            setIsLoading(false);
        }
    };

    /**
     * Clear all images
     */
    const handleClearAll = async () => {
        if (!confirm("Are you sure you want to delete all generated images? This action cannot be undone.")) {
            return;
        }

        try {
            setIsClearing(true);
            const deletedCount = await clearAllImages();
            log.info("Cleared all images", { deletedCount });
            await loadStats(); // Refresh stats
        } catch (error) {
            log.error("Failed to clear images", { error });
        } finally {
            setIsClearing(false);
        }
    };

    /**
     * Clean up expired and temporary images
     */
    const handleCleanup = async () => {
        try {
            setIsCleaningUp(true);
            const expiredCount = await cleanupExpiredImages();
            const tempCount = await cleanupTemporaryImages(24); // Clean temp images older than 24 hours
            log.info("Cleaned up images", { expiredCount, tempCount });
            await loadStats(); // Refresh stats
        } catch (error) {
            log.error("Failed to cleanup images", { error });
        } finally {
            setIsCleaningUp(false);
        }
    };

    /**
     * Update storage settings
     */
    const updateSettings = (updates: Partial<typeof imageStorageSettings>) => {
        setImageStorageSettings({
            ...imageStorageSettings,
            ...updates,
        });
    };

    // Load stats on component mount
    useEffect(() => {
        loadStats();
    }, []);

    return (
        <div className="space-y-6">
            {/* Storage Overview */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <HardDrive className="h-5 w-5" />
                        Storage Overview
                        <Button
                            size="sm"
                            variant="ghost"
                            onClick={loadStats}
                            disabled={isLoading}
                        >
                            <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
                        </Button>
                    </CardTitle>
                    <CardDescription>
                        View and manage your generated image storage
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    {isLoading ? (
                        <div className="text-center py-8 text-muted-foreground">
                            Loading storage statistics...
                        </div>
                    ) : stats ? (
                        <div className="space-y-4">
                            {/* Summary Stats */}
                            <div className="grid grid-cols-2 gap-4">
                                <div className="p-4 rounded-lg bg-muted/50 border">
                                    <div className="text-2xl font-bold">{stats.totalImages}</div>
                                    <div className="text-sm text-muted-foreground">Total Images</div>
                                </div>
                                <div className="p-4 rounded-lg bg-muted/50 border">
                                    <div className="text-2xl font-bold">{formatBytes(stats.totalSize)}</div>
                                    <div className="text-sm text-muted-foreground">Storage Used</div>
                                </div>
                            </div>

                            {/* Model Breakdown */}
                            {Object.keys(stats.imagesByModel).length > 0 && (
                                <div>
                                    <h4 className="text-sm font-medium mb-2">Images by Model</h4>
                                    <div className="space-y-2">
                                        {Object.entries(stats.imagesByModel).map(([model, count]) => (
                                            <div key={model} className="flex justify-between text-sm">
                                                <span className="text-muted-foreground">{model}</span>
                                                <span>{count} images</span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {/* Date Range */}
                            {stats.oldestImage && stats.newestImage && (
                                <div>
                                    <h4 className="text-sm font-medium mb-2">Date Range</h4>
                                    <div className="text-sm text-muted-foreground">
                                        {stats.oldestImage.toLocaleDateString()} - {stats.newestImage.toLocaleDateString()}
                                    </div>
                                </div>
                            )}
                        </div>
                    ) : (
                        <div className="text-center py-8 text-muted-foreground">
                            No images stored
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Privacy Controls */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Shield className="h-5 w-5" />
                        Privacy Controls
                    </CardTitle>
                    <CardDescription>
                        Control how your generated images are stored and managed
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Auto-cleanup Settings */}
                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <div className="text-sm font-medium">Automatic Cleanup</div>
                            <div className="text-sm text-muted-foreground">
                                Automatically delete old images to save storage
                            </div>
                        </div>
                        <Switch
                            checked={imageStorageSettings.autoCleanup}
                            onCheckedChange={(checked) => updateSettings({ autoCleanup: checked })}
                        />
                    </div>

                    {/* Cleanup Interval */}
                    {imageStorageSettings.autoCleanup && (
                        <div className="ml-4 space-y-2">
                            <div className="text-sm font-medium">Delete images older than</div>
                            <Select
                                value={imageStorageSettings.cleanupAfterDays.toString()}
                                onValueChange={(value) => updateSettings({ cleanupAfterDays: parseInt(value) })}
                            >
                                <SelectTrigger className="w-48">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="7">7 days</SelectItem>
                                    <SelectItem value="14">14 days</SelectItem>
                                    <SelectItem value="30">30 days</SelectItem>
                                    <SelectItem value="90">90 days</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    )}

                    <Separator />

                    {/* Export Settings */}
                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <div className="text-sm font-medium">Include Images in Exports</div>
                            <div className="text-sm text-muted-foreground">
                                Include generated images when exporting conversations
                            </div>
                        </div>
                        <Switch
                            checked={imageStorageSettings.includeInExports}
                            onCheckedChange={(checked) => updateSettings({ includeInExports: checked })}
                        />
                    </div>

                    {/* Compression Settings */}
                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <div className="text-sm font-medium">Compress Images</div>
                            <div className="text-sm text-muted-foreground">
                                Reduce file size to save storage space
                            </div>
                        </div>
                        <Switch
                            checked={imageStorageSettings.compressImages}
                            onCheckedChange={(checked) => updateSettings({ compressImages: checked })}
                        />
                    </div>
                </CardContent>
            </Card>

            {/* Storage Management */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Settings className="h-5 w-5" />
                        Storage Management
                    </CardTitle>
                    <CardDescription>
                        Manage your image storage and free up space
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Cleanup Actions */}
                    <div className="flex gap-2">
                        <Button
                            variant="outline"
                            onClick={handleCleanup}
                            disabled={isCleaningUp}
                            className="flex-1"
                        >
                            {isCleaningUp ? (
                                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                                <Trash2 className="h-4 w-4 mr-2" />
                            )}
                            Clean Up Old Images
                        </Button>
                        
                        <Button
                            variant="destructive"
                            onClick={handleClearAll}
                            disabled={isClearing || !stats?.totalImages}
                            className="flex-1"
                        >
                            {isClearing ? (
                                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                                <Trash2 className="h-4 w-4 mr-2" />
                            )}
                            Delete All Images
                        </Button>
                    </div>

                    {/* Warning */}
                    <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                            Deleted images cannot be recovered. Consider exporting important conversations 
                            before clearing your image storage.
                        </AlertDescription>
                    </Alert>
                </CardContent>
            </Card>
        </div>
    );
};
