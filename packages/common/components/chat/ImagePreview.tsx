import type { ImageContent } from "@repo/shared/types";
import { <PERSON>ton, Dialog, DialogContent, DialogTrigger, Progress } from "@repo/ui";
import { AlertCircle, Copy, Download, Expand, RefreshCw } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";

interface ImagePreviewProps {
    imageContent: ImageContent;
    isGenerating?: boolean;
    generationProgress?: number;
    error?: string;
    onRetry?: () => void;
    className?: string;
}

export const ImagePreview = ({
    imageContent,
    isGenerating = false,
    generationProgress = 0,
    error,
    onRetry,
    className = "",
}: ImagePreviewProps) => {
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [imageUrl, setImageUrl] = useState<string>("");
    const [isLoading, setIsLoading] = useState(true);

    // Convert Uint8Array to blob URL for display
    useEffect(() => {
        if (imageContent.imageUrl) {
            setImageUrl(imageContent.imageUrl);
            setIsLoading(false);
        } else if (imageContent.imageData) {
            const blob = new Blob([imageContent.imageData], { type: "image/png" });
            const url = URL.createObjectURL(blob);
            setImageUrl(url);
            setIsLoading(false);

            // Cleanup blob URL on unmount
            return () => URL.revokeObjectURL(url);
        }
    }, [imageContent]);

    const handleDownload = () => {
        if (!imageUrl) return;

        const link = document.createElement("a");
        link.href = imageUrl;
        link.download = `generated-image-${Date.now()}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleCopyPrompt = async () => {
        try {
            await navigator.clipboard.writeText(imageContent.prompt);
        } catch (err) {
            // Silently handle clipboard errors
        }
    };

    // Loading state during generation
    if (isGenerating) {
        return (
            <div className={`relative max-w-md rounded-lg border bg-muted/50 p-6 ${className}`}>
                <div className="flex flex-col items-center space-y-4">
                    <div className="animate-pulse bg-muted rounded-lg w-full aspect-square flex items-center justify-center">
                        <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
                    </div>

                    <div className="w-full space-y-2">
                        <div className="flex justify-between text-sm text-muted-foreground">
                            <span>Generating image...</span>
                            <span>{Math.round(generationProgress)}%</span>
                        </div>
                        <Progress value={generationProgress} className="w-full" />
                    </div>

                    <div className="text-sm text-muted-foreground text-center">
                        <p className="font-medium">Prompt:</p>
                        <p className="mt-1 text-xs">{imageContent.prompt}</p>
                    </div>
                </div>
            </div>
        );
    }

    // Error state
    if (error) {
        return (
            <div
                className={`relative max-w-md rounded-lg border border-destructive/20 bg-destructive/5 p-6 ${className}`}
            >
                <div className="flex flex-col items-center space-y-4">
                    <div className="bg-destructive/10 rounded-lg w-full aspect-square flex items-center justify-center">
                        <AlertCircle className="h-8 w-8 text-destructive" />
                    </div>

                    <div className="text-center space-y-2">
                        <p className="text-sm font-medium text-destructive">Generation Failed</p>
                        <p className="text-xs text-muted-foreground">{error}</p>
                    </div>

                    {onRetry && (
                        <Button size="sm" variant="outline" onClick={onRetry} className="w-full">
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Retry Generation
                        </Button>
                    )}

                    <div className="text-sm text-muted-foreground text-center">
                        <p className="font-medium">Prompt:</p>
                        <p className="mt-1 text-xs">{imageContent.prompt}</p>
                    </div>
                </div>
            </div>
        );
    }

    // Loading state for image processing
    if (isLoading || !imageUrl) {
        return (
            <div className={`relative max-w-md rounded-lg border bg-muted/50 p-6 ${className}`}>
                <div className="animate-pulse bg-muted rounded-lg w-full aspect-square" />
                <div className="mt-2 space-y-2">
                    <div className="h-4 bg-muted rounded w-3/4" />
                    <div className="h-3 bg-muted rounded w-1/2" />
                </div>
            </div>
        );
    }

    // Success state - display generated image
    return (
        <div
            className={`relative max-w-md rounded-lg overflow-hidden shadow-lg border ${className}`}
        >
            <div className="relative group">
                <Image
                    alt={imageContent.prompt}
                    className="w-full h-auto rounded-t-lg"
                    src={imageUrl}
                    width={512}
                    height={512}
                    priority
                    onError={() => setIsLoading(false)}
                />

                {/* Image overlay with actions */}
                <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-200 group">
                    <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <Dialog open={isFullscreen} onOpenChange={setIsFullscreen}>
                            <DialogTrigger asChild>
                                <Button
                                    size="icon-sm"
                                    variant="secondary"
                                    className="bg-black/50 hover:bg-black/70 text-white border-none"
                                    title="View fullscreen"
                                >
                                    <Expand size={14} />
                                </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-4xl max-h-[90vh] p-0">
                                <div className="relative">
                                    <Image
                                        alt={imageContent.prompt}
                                        className="w-full h-auto"
                                        src={imageUrl}
                                        width={1024}
                                        height={1024}
                                        priority
                                    />
                                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent text-white p-6">
                                        <div className="space-y-2">
                                            <p className="text-sm font-medium">Prompt:</p>
                                            <p className="text-sm">{imageContent.prompt}</p>
                                            <div className="flex gap-2 text-xs text-white/70">
                                                <span>Model: {imageContent.model}</span>
                                                {imageContent.aspectRatio && (
                                                    <span>
                                                        • Aspect Ratio: {imageContent.aspectRatio}
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </DialogContent>
                        </Dialog>

                        <Button
                            size="icon-sm"
                            variant="secondary"
                            className="bg-black/50 hover:bg-black/70 text-white border-none"
                            onClick={handleDownload}
                            title="Download image"
                        >
                            <Download size={14} />
                        </Button>
                    </div>
                </div>
            </div>

            {/* Image metadata and actions */}
            <div className="p-3 bg-background border-t space-y-3">
                {/* Prompt display */}
                <div className="space-y-1">
                    <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-foreground">Prompt</p>
                        <Button
                            size="icon-sm"
                            variant="ghost"
                            onClick={handleCopyPrompt}
                            title="Copy prompt"
                            className="h-6 w-6"
                        >
                            <Copy size={12} />
                        </Button>
                    </div>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                        {imageContent.prompt}
                    </p>
                </div>

                {/* Model and metadata */}
                <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
                    <span className="bg-muted px-2 py-1 rounded">Model: {imageContent.model}</span>
                    {imageContent.aspectRatio && (
                        <span className="bg-muted px-2 py-1 rounded">
                            Ratio: {imageContent.aspectRatio}
                        </span>
                    )}
                </div>
            </div>
        </div>
    );
};
